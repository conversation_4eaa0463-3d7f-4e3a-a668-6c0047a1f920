{"name": "@nurse-platform/backend", "version": "0.0.1", "private": true, "nx": {"targets": {"build": {"executor": "nx:run-commands", "options": {"command": "webpack-cli build", "args": ["node-env=production"]}, "configurations": {"development": {"args": ["node-env=development"]}}}, "serve": {"continuous": true, "executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "@nurse-platform/backend:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "@nurse-platform/backend:build:development"}, "production": {"buildTarget": "@nurse-platform/backend:build:production"}}}}}}