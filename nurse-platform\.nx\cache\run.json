{"run": {"command": "nx run @nurse-platform/backend:build:development", "startTime": "2025-06-26T11:52:43.822Z", "endTime": "2025-06-26T11:52:50.029Z", "inner": false}, "tasks": [{"taskId": "@nurse-platform/backend:build:development", "target": "build", "projectName": "@nurse-platform/backend", "hash": "15945602143682684156", "startTime": "2025-06-26T11:52:43.866Z", "endTime": "2025-06-26T11:52:49.942Z", "params": "", "cacheStatus": "cache-miss", "status": 0}]}