{"5306213721880821984": {"build": {"command": "next build", "options": {"cwd": "apps/nurse-platform", "tty": false}, "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/apps/nurse-platform/.next/!(cache)/**/*", "{workspaceRoot}/apps/nurse-platform/.next/!(cache)"]}, "dev": {"continuous": true, "command": "next dev", "options": {"cwd": "apps/nurse-platform"}}, "start": {"continuous": true, "command": "next start", "options": {"cwd": "apps/nurse-platform"}, "dependsOn": ["build"]}, "serve-static": {"continuous": true, "command": "next start", "options": {"cwd": "apps/nurse-platform"}, "dependsOn": ["build"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/nurse-platform --includeDependentProjects -- npx nx build-deps @nurse-platform/nurse-platform"}}, "14095097865175809280": {"build": {"command": "next build", "options": {"cwd": "apps/nurse-platform", "tty": false}, "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/apps/nurse-platform/.next/!(cache)/**/*", "{workspaceRoot}/apps/nurse-platform/.next/!(cache)"]}, "dev": {"continuous": true, "command": "next dev", "options": {"cwd": "apps/nurse-platform"}}, "start": {"continuous": true, "command": "next start", "options": {"cwd": "apps/nurse-platform"}, "dependsOn": ["build"]}, "serve-static": {"continuous": true, "command": "next start", "options": {"cwd": "apps/nurse-platform"}, "dependsOn": ["build"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/nurse-platform --includeDependentProjects -- npx nx build-deps @nurse-platform/nurse-platform"}}, "5542053271417882433": {"build": {"command": "next build", "options": {"cwd": "frontend", "tty": false}, "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/frontend/.next/!(cache)/**/*", "{workspaceRoot}/frontend/.next/!(cache)"]}, "dev": {"continuous": true, "command": "next dev", "options": {"cwd": "frontend"}}, "start": {"continuous": true, "command": "next start", "options": {"cwd": "frontend"}, "dependsOn": ["build"]}, "serve-static": {"continuous": true, "command": "next start", "options": {"cwd": "frontend"}, "dependsOn": ["build"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/frontend --includeDependentProjects -- npx nx build-deps @nurse-platform/frontend"}}, "9470555518627105090": {"build": {"command": "next build", "options": {"cwd": "apps/frontend", "tty": false}, "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/apps/frontend/.next/!(cache)/**/*", "{workspaceRoot}/apps/frontend/.next/!(cache)"]}, "dev": {"continuous": true, "command": "next dev", "options": {"cwd": "apps/frontend"}}, "start": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "serve-static": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/frontend --includeDependentProjects -- npx nx build-deps @nurse-platform/frontend"}}, "8494387861618723435": {"build": {"command": "next build", "options": {"cwd": "apps/nurse-platform", "tty": false}, "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/apps/nurse-platform/.next/!(cache)/**/*", "{workspaceRoot}/apps/nurse-platform/.next/!(cache)"]}, "dev": {"continuous": true, "command": "next dev", "options": {"cwd": "apps/nurse-platform"}}, "start": {"continuous": true, "command": "next start", "options": {"cwd": "apps/nurse-platform"}, "dependsOn": ["build"]}, "serve-static": {"continuous": true, "command": "next start", "options": {"cwd": "apps/nurse-platform"}, "dependsOn": ["build"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/nurse-platform --includeDependentProjects -- npx nx build-deps @nurse-platform/nurse-platform"}}, "17426284472122055883": {"build": {"command": "next build", "options": {"cwd": "apps/frontend", "tty": false}, "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/apps/frontend/.next/!(cache)/**/*", "{workspaceRoot}/apps/frontend/.next/!(cache)"]}, "dev": {"continuous": true, "command": "next dev", "options": {"cwd": "apps/frontend"}}, "start": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "serve-static": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/frontend --includeDependentProjects -- npx nx build-deps @nurse-platform/frontend"}}, "17269386187583951368": {"build": {"command": "next build", "options": {"cwd": "apps/nurse-platform", "tty": false}, "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/apps/nurse-platform/.next/!(cache)/**/*", "{workspaceRoot}/apps/nurse-platform/.next/!(cache)"]}, "dev": {"continuous": true, "command": "next dev", "options": {"cwd": "apps/nurse-platform"}}, "start": {"continuous": true, "command": "next start", "options": {"cwd": "apps/nurse-platform"}, "dependsOn": ["build"]}, "serve-static": {"continuous": true, "command": "next start", "options": {"cwd": "apps/nurse-platform"}, "dependsOn": ["build"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/nurse-platform --includeDependentProjects -- npx nx build-deps @nurse-platform/nurse-platform"}}, "3663666069111542098": {"build": {"command": "next build", "options": {"cwd": "apps/frontend", "tty": false}, "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/apps/frontend/.next/!(cache)/**/*", "{workspaceRoot}/apps/frontend/.next/!(cache)"]}, "dev": {"continuous": true, "command": "next dev", "options": {"cwd": "apps/frontend"}}, "start": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "serve-static": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/frontend --includeDependentProjects -- npx nx build-deps @nurse-platform/frontend"}}, "6108718808535093214": {"build": {"command": "next build", "options": {"cwd": "apps/nurse-platform", "tty": false}, "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/apps/nurse-platform/.next/!(cache)/**/*", "{workspaceRoot}/apps/nurse-platform/.next/!(cache)"]}, "dev": {"continuous": true, "command": "next dev", "options": {"cwd": "apps/nurse-platform"}}, "start": {"continuous": true, "command": "next start", "options": {"cwd": "apps/nurse-platform"}, "dependsOn": ["build"]}, "serve-static": {"continuous": true, "command": "next start", "options": {"cwd": "apps/nurse-platform"}, "dependsOn": ["build"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/nurse-platform --includeDependentProjects -- npx nx build-deps @nurse-platform/nurse-platform"}}, "11802337264274520382": {"build": {"command": "next build", "options": {"cwd": "apps/frontend", "tty": false}, "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/apps/frontend/.next/!(cache)/**/*", "{workspaceRoot}/apps/frontend/.next/!(cache)"]}, "dev": {"continuous": true, "command": "next dev", "options": {"cwd": "apps/frontend"}}, "start": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "serve-static": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/frontend --includeDependentProjects -- npx nx build-deps @nurse-platform/frontend"}}, "12427304126531406504": {"build": {"command": "next build", "options": {"cwd": "apps/frontend", "tty": false}, "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/apps/frontend/.next/!(cache)/**/*", "{workspaceRoot}/apps/frontend/.next/!(cache)"]}, "dev": {"continuous": true, "command": "next dev", "options": {"cwd": "apps/frontend"}}, "start": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "serve-static": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/frontend --includeDependentProjects -- npx nx build-deps @nurse-platform/frontend"}}, "16529264799863742335": {"build": {"command": "next build", "options": {"cwd": "apps/frontend", "tty": false}, "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/apps/frontend/.next/!(cache)/**/*", "{workspaceRoot}/apps/frontend/.next/!(cache)"]}, "dev": {"continuous": true, "command": "next dev", "options": {"cwd": "apps/frontend"}}, "start": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "serve-static": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/frontend --includeDependentProjects -- npx nx build-deps @nurse-platform/frontend"}}, "6658595525035983695": {"build": {"command": "next build", "options": {"cwd": "apps/frontend", "tty": false}, "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/apps/frontend/.next/!(cache)/**/*", "{workspaceRoot}/apps/frontend/.next/!(cache)"]}, "dev": {"continuous": true, "command": "next dev", "options": {"cwd": "apps/frontend"}}, "start": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "serve-static": {"continuous": true, "command": "next start", "options": {"cwd": "apps/frontend"}, "dependsOn": ["build"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/frontend --includeDependentProjects -- npx nx build-deps @nurse-platform/frontend"}}}