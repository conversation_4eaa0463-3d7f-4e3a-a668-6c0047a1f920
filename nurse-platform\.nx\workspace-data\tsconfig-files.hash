{"version": 1, "data": {"tsconfig.base.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": []}, "extendedFilesHash": "", "hash": "8151884466140505987"}, "apps\\nurse-platform\\tsconfig.json": {"data": {"options": {"noEmit": true, "rootDir": "apps\\nurse-platform\\src", "outDir": "apps\\nurse-platform\\dist", "tsBuildInfoFile": "apps\\nurse-platform\\dist\\tsconfig.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}]}, "extendedFilesHash": "8151884466140505987|", "hash": "14282647671993012122"}, "tsconfig.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "apps\\backend-e2e", "originalPath": "./apps/backend-e2e"}, {"path": "apps\\frontend", "originalPath": "./apps/frontend"}, {"path": "apps\\backend", "originalPath": "./apps/backend"}]}, "extendedFilesHash": "8151884466140505987|", "hash": "15377970102068836148"}, "frontend\\tsconfig.json": {"data": {"options": {"noEmit": true, "rootDir": "frontend\\src", "outDir": "frontend\\dist", "tsBuildInfoFile": "frontend\\dist\\tsconfig.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}]}, "extendedFilesHash": "8151884466140505987|", "hash": "9516674429048752827"}, "apps\\frontend\\tsconfig.json": {"data": {"options": {"noEmit": true, "rootDir": "apps\\frontend\\src", "outDir": "apps\\frontend\\dist", "tsBuildInfoFile": "apps\\frontend\\dist\\tsconfig.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}]}, "extendedFilesHash": "8151884466140505987|", "hash": "13947792926974068550"}, "backend\\tsconfig.app.json": {"data": {"options": {"rootDir": "backend\\src", "outDir": "backend\\dist", "tsBuildInfoFile": "backend\\dist\\tsconfig.app.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}]}, "extendedFilesHash": "8151884466140505987|", "hash": "3459959380386097172"}, "backend\\tsconfig.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "backend\\tsconfig.app.json", "originalPath": "./tsconfig.app.json"}]}, "extendedFilesHash": "8151884466140505987|", "hash": "15689909388364298010"}, "backend-e2e\\tsconfig.json": {"data": {"options": {"outDir": "backend-e2e\\out-tsc\\@nurse-platform\\backend-e2e"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}]}, "extendedFilesHash": "8151884466140505987|", "hash": "14919211324693497486"}, "apps\\backend\\tsconfig.app.json": {"data": {"options": {"rootDir": "apps\\backend\\src", "outDir": "apps\\backend\\dist", "tsBuildInfoFile": "apps\\backend\\dist\\tsconfig.app.tsbuildinfo"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}]}, "extendedFilesHash": "8151884466140505987|", "hash": "17980311448850599591"}, "apps\\backend\\tsconfig.json": {"data": {"options": {}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}], "projectReferences": [{"path": "apps\\backend\\tsconfig.app.json", "originalPath": "./tsconfig.app.json"}]}, "extendedFilesHash": "8151884466140505987|", "hash": "6240538891403235665"}, "apps\\backend-e2e\\tsconfig.json": {"data": {"options": {"outDir": "apps\\backend-e2e\\out-tsc\\@nurse-platform\\backend-e2e"}, "raw": {"nx": {}}, "extendedConfigFiles": [{"filePath": "tsconfig.base.json"}]}, "extendedFilesHash": "8151884466140505987|", "hash": "14934646430113733585"}}}