{"apps/backend": {"root": ["apps/backend/package.json", "nx/core/package-json"], "projectType": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets": ["apps/backend/tsconfig.app.json", "@nx/js/typescript"], "targets.typecheck": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.dependsOn": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.cache": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.inputs": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.outputs": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.syncGenerators": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.executor": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.cwd": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.command": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies.0": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.description": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.command": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.example": ["apps/backend/tsconfig.json", "@nx/js/typescript"], "targets.build": ["apps/backend/package.json", "nx/core/package-json"], "targets.build.options": ["apps/backend/package.json", "nx/core/package-json"], "targets.build.cache": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.dependsOn": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.inputs": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.outputs": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.syncGenerators": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.executor": ["apps/backend/package.json", "nx/core/package-json"], "targets.build.options.cwd": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.options.args": ["apps/backend/package.json", "nx/core/package-json"], "targets.build.options.command": ["apps/backend/package.json", "nx/core/package-json"], "targets.build.metadata.technologies": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.technologies.0": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.description": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.help": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.help.command": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build.metadata.help.example": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.continuous": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.options": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.syncGenerators": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.executor": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.options.cwd": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.options.args": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.options.command": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.technologies": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.technologies.0": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.description": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.help": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.help.command": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.preview.metadata.help.example": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build-deps": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.build-deps.dependsOn": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.continuous": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.dependsOn": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.executor": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.options": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "targets.watch-deps.options.command": ["apps/backend/webpack.config.js", "@nx/webpack/plugin"], "name": ["apps/backend/package.json", "nx/core/package-json"], "tags": ["apps/backend/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/backend/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/backend/package.json", "nx/core/package-json"], "metadata.js": ["apps/backend/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/backend/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/backend/package.json", "nx/core/package-json"], "targets.build.configurations": ["apps/backend/package.json", "nx/core/package-json"], "targets.build.configurations.development": ["apps/backend/package.json", "nx/core/package-json"], "targets.build.configurations.development.args": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.continuous": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.executor": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.defaultConfiguration": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.dependsOn": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.options": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.configurations": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.options.buildTarget": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.options.runBuildTargetDependencies": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.configurations.development": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.configurations.development.buildTarget": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.configurations.production": ["apps/backend/package.json", "nx/core/package-json"], "targets.serve.configurations.production.buildTarget": ["apps/backend/package.json", "nx/core/package-json"]}, "apps/backend-e2e": {"root": ["apps/backend-e2e/package.json", "nx/core/package-json"], "projectType": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.dependsOn": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.cache": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.inputs": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.outputs": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.syncGenerators": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.executor": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.cwd": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.options.command": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.technologies.0": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.description": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.command": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "targets.typecheck.metadata.help.example": ["apps/backend-e2e/tsconfig.json", "@nx/js/typescript"], "name": ["apps/backend-e2e/package.json", "nx/core/package-json"], "tags": ["apps/backend-e2e/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/backend-e2e/package.json", "nx/core/package-json"], "implicitDependencies": ["apps/backend-e2e/package.json", "nx/core/package-json"], "implicitDependencies.@nurse-platform/backend": ["apps/backend-e2e/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/backend-e2e/package.json", "nx/core/package-json"], "metadata.js": ["apps/backend-e2e/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/backend-e2e/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e.executor": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e.outputs": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e.options": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e.dependsOn": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e.options.jestConfig": ["apps/backend-e2e/package.json", "nx/core/package-json"], "targets.e2e.options.passWithNoTests": ["apps/backend-e2e/package.json", "nx/core/package-json"]}, "apps/frontend": {"root": ["apps/frontend/package.json", "nx/core/package-json"], "targets": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.build": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.build.options": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.build.dependsOn": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.build.cache": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.build.inputs": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.build.outputs": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.build.executor": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.build.options.cwd": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.build.options.tty": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.build.options.command": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.dev": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.dev.continuous": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.dev.options": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.dev.executor": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.dev.options.cwd": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.dev.options.command": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.start": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.start.continuous": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.start.options": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.start.dependsOn": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.start.executor": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.start.options.cwd": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.start.options.command": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.serve-static": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.serve-static.continuous": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.serve-static.options": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.serve-static.dependsOn": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.serve-static.executor": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.serve-static.options.cwd": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.serve-static.options.command": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.build-deps": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.build-deps.dependsOn": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.watch-deps": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.watch-deps.continuous": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.watch-deps.dependsOn": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.watch-deps.executor": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.watch-deps.options": ["apps/frontend/next.config.js", "@nx/next/plugin"], "targets.watch-deps.options.command": ["apps/frontend/next.config.js", "@nx/next/plugin"], "name": ["apps/frontend/package.json", "nx/core/package-json"], "tags": ["apps/frontend/package.json", "nx/core/package-json"], "tags.npm:private": ["apps/frontend/package.json", "nx/core/package-json"], "metadata.targetGroups": ["apps/frontend/package.json", "nx/core/package-json"], "metadata.js": ["apps/frontend/package.json", "nx/core/package-json"], "metadata.js.packageName": ["apps/frontend/package.json", "nx/core/package-json"], "metadata.js.isInPackageManagerWorkspaces": ["apps/frontend/package.json", "nx/core/package-json"]}}