{"version": "6.0", "nxVersion": "21.2.1", "pathMappings": {}, "nxJsonPlugins": [{"name": "@nx/js/typescript", "options": {"typecheck": {"targetName": "typecheck"}, "build": {"targetName": "build", "configName": "tsconfig.lib.json", "buildDepsName": "build-deps", "watchDepsName": "watch-deps"}}}, {"name": "@nx/next/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "devTargetName": "dev", "serveStaticTargetName": "serve-static", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"name": "@nx/webpack/plugin", "options": {"buildTargetName": "build", "serveTargetName": "serve", "previewTargetName": "preview", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}], "fileMap": {"projectFileMap": {"@nurse-platform/backend-e2e": [{"file": "apps/backend-e2e/.spec.swcrc", "hash": "18201067170784284381"}, {"file": "apps/backend-e2e/jest.config.ts", "hash": "12740493828304542877"}, {"file": "apps/backend-e2e/package.json", "hash": "16883686026596545556"}, {"file": "apps/backend-e2e/src/backend/backend.spec.ts", "hash": "4747019362565984448", "deps": ["npm:axios"]}, {"file": "apps/backend-e2e/src/support/global-setup.ts", "hash": "3444053551545844372", "deps": ["npm:@nx/node"]}, {"file": "apps/backend-e2e/src/support/global-teardown.ts", "hash": "18176614999450167852", "deps": ["npm:@nx/node"]}, {"file": "apps/backend-e2e/src/support/test-setup.ts", "hash": "11749744855473391997", "deps": ["npm:axios"]}, {"file": "apps/backend-e2e/tsconfig.json", "hash": "14934646430113733585"}], "@nurse-platform/frontend": [{"file": "apps/frontend/.gitignore", "hash": "1250600467673810123"}, {"file": "apps/frontend/.swcrc", "hash": "12307600318232351063"}, {"file": "apps/frontend/index.d.ts", "hash": "17978550148300837039"}, {"file": "apps/frontend/next-env.d.ts", "hash": "1320675027584673186"}, {"file": "apps/frontend/next.config.js", "hash": "15676900314487773919", "deps": ["npm:@nx/next"]}, {"file": "apps/frontend/package.json", "hash": "2842931021399508445", "deps": ["npm:next", "npm:react", "npm:react-dom"]}, {"file": "apps/frontend/postcss.config.js", "hash": "15972724582657845432"}, {"file": "apps/frontend/public/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/frontend/public/favicon.ico", "hash": "9303420814833116677"}, {"file": "apps/frontend/src/app/api/hello/route.ts", "hash": "481462152031695647"}, {"file": "apps/frontend/src/app/global.css", "hash": "9232295677464338735"}, {"file": "apps/frontend/src/app/layout.tsx", "hash": "14639536254623571858"}, {"file": "apps/frontend/src/app/page.tsx", "hash": "8682287144833946047"}, {"file": "apps/frontend/tailwind.config.js", "hash": "12678958122524329131"}, {"file": "apps/frontend/tsconfig.json", "hash": "13947792926974068550"}], "@nurse-platform/backend": [{"file": "apps/backend/package.json", "hash": "12118711519988328283"}, {"file": "apps/backend/src/app/app.controller.ts", "hash": "15276891910009544695", "deps": ["npm:@nestjs/common"]}, {"file": "apps/backend/src/app/app.module.ts", "hash": "11673476679848632172", "deps": ["npm:@nestjs/common", "npm:@nestjs/mongoose", "npm:@nestjs/config"]}, {"file": "apps/backend/src/app/app.service.ts", "hash": "1398463054208547248", "deps": ["npm:@nestjs/common"]}, {"file": "apps/backend/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/backend/src/main.ts", "hash": "8808025383060200233", "deps": ["npm:@nestjs/common", "npm:@nestjs/core"]}, {"file": "apps/backend/tsconfig.app.json", "hash": "17980311448850599591"}, {"file": "apps/backend/tsconfig.json", "hash": "6240538891403235665"}, {"file": "apps/backend/webpack.config.js", "hash": "8354317366887649689", "deps": ["npm:@nx/webpack"]}]}, "nonProjectFiles": [{"file": ".editorconfig", "hash": "11532813898455409296"}, {"file": ".giti<PERSON>re", "hash": "14448690875013037399"}, {"file": ".vscode/extensions.json", "hash": "10765520025180859680"}, {"file": "NursePlatform_MVP.pdf", "hash": "10683725758072529811"}, {"file": "Nursing-platform.pdf", "hash": "13412199101660919925"}, {"file": "README.md", "hash": "13882552937363164833"}, {"file": "nx.json", "hash": "1595770163046615983"}, {"file": "package-lock.json", "hash": "8969564959383985909"}, {"file": "package.json", "hash": "6070976153258689186"}, {"file": "tsconfig.base.json", "hash": "8151884466140505987"}, {"file": "tsconfig.json", "hash": "1379983966652230747"}]}}