{"6586250716635761846": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/backend --includeDependentProjects -- npx nx build-deps @nurse-platform/backend"}}, "metadata": {}}, "2178514962644437482": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/backend --includeDependentProjects -- npx nx build-deps @nurse-platform/backend"}}, "metadata": {}}, "15536401369348364441": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/backend --includeDependentProjects -- npx nx build-deps @nurse-platform/backend"}}, "metadata": {}}, "16943457229362393604": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/backend --includeDependentProjects -- npx nx build-deps @nurse-platform/backend"}}, "metadata": {}}, "12205997198287283059": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/backend --includeDependentProjects -- npx nx build-deps @nurse-platform/backend"}}, "metadata": {}}, "15385952044428918016": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/backend --includeDependentProjects -- npx nx build-deps @nurse-platform/backend"}}, "metadata": {}}, "9479079689782420811": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/backend --includeDependentProjects -- npx nx build-deps @nurse-platform/backend"}}, "metadata": {}}, "17770166537149505746": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/backend --includeDependentProjects -- npx nx build-deps @nurse-platform/backend"}}, "metadata": {}}, "8366911215811580811": {"targets": {"build": {"command": "webpack-cli build", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production", {"externalDependencies": ["webpack-cli"]}], "outputs": ["{workspaceRoot}/apps\\backend\\dist"], "metadata": {"technologies": ["webpack"], "description": "Runs Webpack build", "help": {"command": "npx webpack-cli build --help", "example": {"options": {"json": "stats.json"}, "args": ["--profile"]}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=development"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "preview": {"continuous": true, "command": "webpack-cli serve", "options": {"cwd": "apps/backend", "args": ["--node-env=production"]}, "metadata": {"technologies": ["webpack"], "description": "Starts Webpack dev server in production mode", "help": {"command": "npx webpack-cli serve --help", "example": {"options": {"args": ["--client-progress", "--history-api-fallback "]}}}}, "syncGenerators": ["@nx/js:typescript-sync"]}, "serve-static": {"continuous": true, "dependsOn": ["build"], "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}, "syncGenerators": ["@nx/js:typescript-sync"]}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects @nurse-platform/backend --includeDependentProjects -- npx nx build-deps @nurse-platform/backend"}}, "metadata": {}}}