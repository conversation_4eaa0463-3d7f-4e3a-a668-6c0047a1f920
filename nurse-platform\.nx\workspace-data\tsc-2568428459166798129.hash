{"16233629715871859963_backend/tsconfig.app.json": {"targets": {}}, "4441728502973941108_backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "backend"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "2657925519966760957_backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "backend-e2e"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.json", "{projectRoot}/jest.config.ts", "{projectRoot}/src/**/*.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nurse-platform/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nurse-platform/backend-e2e/**/*.d.ts.map", "{projectRoot}/out-tsc/@nurse-platform/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "730891446737278605_backend/tsconfig.app.json": {"targets": {}}, "9330594086252434327_backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "backend"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "11193505805287766184_backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "backend-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nurse-platform/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nurse-platform/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "4468447575861294424_apps/backend/tsconfig.app.json": {"targets": {}}, "99860982400788681_apps/backend/tsconfig.app.json": {"targets": {}}, "9487973184718440784_apps/backend/tsconfig.app.json": {"targets": {}}, "9393384149171212169_apps/backend/tsconfig.app.json": {"targets": {}}, "16858979083039712468_apps/backend/tsconfig.app.json": {"targets": {}}, "15204461917255683359_apps/backend/tsconfig.app.json": {"targets": {}}, "1742294177430402739_apps/backend/tsconfig.app.json": {"targets": {}}, "13736730444828390398_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "1703092286872049357_apps/backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend-e2e"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{projectRoot}/tsconfig.json", "{projectRoot}/jest.config.ts", "{projectRoot}/src/**/*.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nurse-platform/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nurse-platform/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "10521015388229004557_apps/backend/tsconfig.app.json": {"targets": {}}, "12190726671562774439_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "10320860046920545433_apps/backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nurse-platform/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nurse-platform/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "1619512420432736936_apps/backend/tsconfig.app.json": {"targets": {}}, "934654184392311443_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "12514753968740096234_apps/backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nurse-platform/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nurse-platform/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "8292129446974916681_apps/backend/tsconfig.app.json": {"targets": {}}, "15479569668923152140_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{projectRoot}/tsconfig.json", "{projectRoot}/tsconfig.app.json", "{projectRoot}/src/**/*.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "14050953972018872721_apps/backend/tsconfig.app.json": {"targets": {}}, "4239297226030685056_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{projectRoot}/tsconfig.json", "{projectRoot}/tsconfig.app.json", "{projectRoot}/src/**/*.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "4883902078148798922_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["production", {"dependentTasksOutputFiles": "**/*.d.ts"}, {"externalDependencies": ["typescript"]}], "outputs": [], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "16597358101472498871_apps/backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend-e2e"}, "cache": true, "inputs": ["{projectRoot}/package.json", "{workspaceRoot}/tsconfig.base.json", "{projectRoot}/tsconfig.json", "{projectRoot}/jest.config.ts", "{projectRoot}/src/**/*.ts", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nurse-platform/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nurse-platform/backend-e2e/**/*.d.ts.map", "{projectRoot}/out-tsc/@nurse-platform/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "17892236173488051472_apps/backend/tsconfig.app.json": {"targets": {}}, "12403705513950795006_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "2785552577748235017_apps/backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nurse-platform/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nurse-platform/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "13006451180612485098_apps/backend/tsconfig.app.json": {"targets": {}}, "958984457401905975_apps/backend/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/**/*.jsx", "{projectRoot}/**/*.js.map", "{projectRoot}/**/*.jsx.map", "{projectRoot}/**/*.d.ts", "{projectRoot}/**/*.d.cts", "{projectRoot}/**/*.d.mts", "{projectRoot}/**/*.d.ts.map", "{projectRoot}/**/*.d.cts.map", "{projectRoot}/**/*.d.mts.map", "{projectRoot}/tsconfig.tsbuildinfo", "{projectRoot}/dist/**/*.d.ts", "{projectRoot}/dist/tsconfig.app.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}, "12335721591083260641_apps/backend-e2e/tsconfig.json": {"targets": {"typecheck": {"dependsOn": ["^typecheck"], "command": "tsc --build --emitDeclarationOnly", "options": {"cwd": "apps/backend-e2e"}, "cache": true, "inputs": ["production", "^production", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/out-tsc/@nurse-platform/backend-e2e/**/*.d.ts", "{projectRoot}/out-tsc/@nurse-platform/backend-e2e/tsconfig.tsbuildinfo"], "syncGenerators": ["@nx/js:typescript-sync"], "metadata": {"technologies": ["typescript"], "description": "Runs type-checking for the project.", "help": {"command": "npx tsc --build --help", "example": {"args": ["--force"]}}}}}}}