[2m> [22mwebpack-cli build node-env=production

chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 39 bytes [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m

[1m[31mERROR[39m[22m in [1mD:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\apps\backend\tsconfig.app.json[39m[22m
[1m./src/main.ts[39m[22m [1m[32mD:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\apps\backend\tsconfig.app.json[39m[22m
[90m[tsl] [39m[1m[31mERROR[39m[22m
[1m[31m      TS5083: Cannot read file 'D:/Intake45_ITI_9-months_Open_Source/Graduation-Project-ITI/nurse-platform/apps/tsconfig.base.json'.[39m[22m

[1m[31mERROR[39m[22m in [1m./src/main.ts[39m[22m
Module build failed (from ../../node_modules/ts-loader/index.js):
Error: [31merror while parsing tsconfig.json[39m
    at Object.loader (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\ts-loader\dist\index.js:18:18)

webpack compiled with [1m[31m2 errors[39m[22m (983d954e1b64ccf8)
[2m> [22mwebpack-cli build node-env=production

chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 39 bytes [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m

[1m[31mERROR[39m[22m in [1mD:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\apps\backend\tsconfig.app.json[39m[22m
[1m./src/main.ts[39m[22m [1m[32mD:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\apps\backend\tsconfig.app.json[39m[22m
[90m[tsl] [39m[1m[31mERROR[39m[22m
[1m[31m      TS5083: Cannot read file 'D:/Intake45_ITI_9-months_Open_Source/Graduation-Project-ITI/nurse-platform/apps/tsconfig.base.json'.[39m[22m

[1m[31mERROR[39m[22m in [1m./src/main.ts[39m[22m
Module build failed (from ../../node_modules/ts-loader/index.js):
Error: [31merror while parsing tsconfig.json[39m
    at Object.loader (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\ts-loader\dist\index.js:18:18)

webpack compiled with [1m[31m2 errors[39m[22m (983d954e1b64ccf8)
Warning: command "webpack-cli build node-env=production" exited with non-zero status code