
[2m> [22m[2mnx run[22m @nurse-platform/backend:build:development

[2m> [22mwebpack-cli build node-env=development

chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 2.72 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
webpack compiled [1m[32msuccessfully[39m[22m (8200d354a2a1a69b)



[0m[7m[1m[32m NX [39m[22m[27m[0m  [32mSuccessfully ran target [1mbuild[22m for project @nurse-platform/backend[39m


[1m[31mDebugger listening on ws://localhost:9229/641d08e0-098e-45e6-97e0-f2635faa71c2[39m[22m
[1m[31mDebugger listening on ws://localhost:9229/641d08e0-098e-45e6-97e0-f2635faa71c2[39m[22m
[1m[31mFor help, see: https://nodejs.org/en/docs/inspector[39m[22m
[1m[31m[39m[22m
[32m[Nest] 572  - [39m06/24/2025, 2:37:29 PM [32m    LOG[39m [38;5;3m[NestFactory] [39m[32mStarting Nest application...[39m
[32m[Nest] 572  - [39m06/24/2025, 2:37:29 PM [32m    LOG[39m [38;5;3m[InstanceLoader] [39m[32mAppModule dependencies initialized[39m[38;5;3m +15ms[39m
[32m[Nest] 572  - [39m06/24/2025, 2:37:29 PM [32m    LOG[39m [38;5;3m[RoutesResolver] [39m[32mAppController {/api}:[39m[38;5;3m +9ms[39m
[32m[Nest] 572  - [39m06/24/2025, 2:37:29 PM [32m    LOG[39m [38;5;3m[RouterExplorer] [39m[32mMapped {/api, GET} route[39m[38;5;3m +7ms[39m
[32m[Nest] 572  - [39m06/24/2025, 2:37:29 PM [32m    LOG[39m [38;5;3m[NestApplication] [39m[32mNest application successfully started[39m[38;5;3m +4ms[39m
[32m[Nest] 572  - [39m06/24/2025, 2:37:29 PM [32m    LOG[39m [32m🚀 Application is running on: http://localhost:3000/api[39m

[7m[1m[36m NX [39m[22m[27m [1mFile change detected. Restarting...[22m


[0m[7m[1m[31m NX [39m[22m[27m[0m  [31mCannot find configuration for task @nurse-platform/backend:build[39m

Pass --verbose to see the stacktrace.

[1m[31mBuild failed, waiting for changes to restart...[39m[22m

[7m[1m[36m NX [39m[22m[27m [1mFile change detected. Restarting...[22m


[7m[1m[36m NX [39m[22m[27m [1mFile change detected. Restarting...[22m


[0m[7m[1m[31m NX [39m[22m[27m[0m  [31mCannot find configuration for task @nurse-platform/backend:build[39m

Pass --verbose to see the stacktrace.

[1m[31mBuild failed, waiting for changes to restart...[39m[22m

[7m[1m[36m NX [39m[22m[27m [1mFile change detected. Restarting...[22m


[0m[7m[1m[31m NX [39m[22m[27m[0m  [31mCannot find configuration for task @nurse-platform/backend:build[39m

Pass --verbose to see the stacktrace.

[1m[31mBuild failed, waiting for changes to restart...[39m[22m

[0m[7m[1m[31m NX [39m[22m[27m[0m  [31mCannot find configuration for task @nurse-platform/backend:build[39m

Pass --verbose to see the stacktrace.


[7m[1m[36m NX [39m[22m[27m [1mFile change detected. Restarting...[22m

[1m[31mBuild failed, waiting for changes to restart...[39m[22m

[7m[1m[36m NX [39m[22m[27m [1mFile change detected. Restarting...[22m


[0m[7m[1m[31m NX [39m[22m[27m[0m  [31mCannot find configuration for task @nurse-platform/backend:build[39m

Pass --verbose to see the stacktrace.

[1m[31mBuild failed, waiting for changes to restart...[39m[22m

[0m[7m[1m[31m NX [39m[22m[27m[0m  [31mCannot find configuration for task @nurse-platform/backend:build[39m

Pass --verbose to see the stacktrace.


[7m[1m[36m NX [39m[22m[27m [1mFile change detected. Restarting...[22m

[1m[31mBuild failed, waiting for changes to restart...[39m[22m

[0m[7m[1m[31m NX [39m[22m[27m[0m  [31mCannot find configuration for task @nurse-platform/backend:build[39m

Pass --verbose to see the stacktrace.


[7m[1m[36m NX [39m[22m[27m [1mFile change detected. Restarting...[22m

[1m[31mBuild failed, waiting for changes to restart...[39m[22m

[0m[7m[1m[31m NX [39m[22m[27m[0m  [31mCannot find configuration for task @nurse-platform/backend:build[39m

Pass --verbose to see the stacktrace.

[1m[31mWatch error: Daemon closed the connection[39m[22m
