[NX v21.2.1 Daemon Server] - 2025-06-24T11:28:54.693Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\f3183296a9513023b006\d.sock
[NX v21.2.1 Daemon Server] - 2025-06-24T11:28:54.700Z - [WATCHER]: Subscribed to changes within: D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (native)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:28:54.706Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:28:54.711Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:28:54.713Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:28:54.717Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:28:56.717Z - Time taken for 'Load Nx Plugin: D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1925.1462ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:28:56.951Z - Time taken for 'Load Nx Plugin: D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\package-json' 2167.3696ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:28:57.867Z - Time taken for 'loadDefaultNxPlugins' 3085.1625000000004ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:01.495Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 6774.982599999999ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:05.885Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:05.885Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:05.885Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:05.910Z - Time taken for 'loadSpecifiedNxPlugins' 11164.4408ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:05.914Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:05.915Z - Established a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:05.916Z - Closed a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:05.922Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:05.923Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:05.923Z - Handled HASH_GLOB. Handling time: 2. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:15.086Z - Time taken for 'build-project-configs' 9140.082400000001ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:15.087Z - Time taken for '@nx/js/typescript:createDependencies' 7.801500000001397ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:15.448Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:15.451Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:15.452Z - Time taken for 'total for creating and serializing project graph' 20733.8477ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:15.454Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:15.454Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 20734. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.298Z - Established a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.300Z - Closed a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.305Z - Established a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.305Z - Closed a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.306Z - Established a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.310Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.311Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.313Z - Time taken for 'total for creating and serializing project graph' 1.221799999999348ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.313Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.314Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.327Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.329Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.329Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:29:20.330Z - Closed a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.278Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.279Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.280Z - Time taken for 'total for creating and serializing project graph' 0.9189000000042142ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.281Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.281Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.341Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.342Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.342Z - Time taken for 'total for creating and serializing project graph' 0.8018000000010943ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.343Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.343Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.358Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.359Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.360Z - Time taken for 'total for creating and serializing project graph' 0.8032999999995809ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.360Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.360Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.366Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.367Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.367Z - Time taken for 'total for creating and serializing project graph' 0.8401000000012573ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.368Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.368Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.373Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.374Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.375Z - Time taken for 'total for creating and serializing project graph' 0.9038000000000466ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.376Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:16.376Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:17.428Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:17.430Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:17.431Z - Time taken for 'total for creating and serializing project graph' 2.5522000000055414ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:17.432Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:17.432Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:17.545Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:17.548Z - [WATCHER]: 0 file(s) created or restored, 18 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:17.551Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:17.551Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:17.556Z - [WATCHER]: Stopping the watcher for D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (sources)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:17.556Z - [WATCHER]: Stopping the watcher for D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (outputs)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:17.562Z - Server stopped because: "Stopping the daemon the set of ignored files changed (native)"
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:27.185Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\f3183296a9513023b006\d.sock
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:27.191Z - [WATCHER]: Subscribed to changes within: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (native)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:27.196Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:27.202Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:27.241Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:27.241Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:27.243Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:27.249Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:28.261Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:28.262Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:28.729Z - Time taken for 'Load Nx Plugin: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1466.1455999999998ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:28.804Z - Time taken for 'Load Nx Plugin: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\package-json' 1542.826ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:29.094Z - Time taken for 'loadDefaultNxPlugins' 1835.2022ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:30.010Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 2758.9961000000003ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:30.074Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:30.074Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:30.074Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:30.141Z - Time taken for 'loadSpecifiedNxPlugins' 2821.8457ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:30.142Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:30.143Z - Established a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:30.151Z - Closed a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:30.180Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:30.181Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:30.181Z - Handled HASH_GLOB. Handling time: 26. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.317Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.317Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.317Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.390Z - Time taken for 'build-project-configs' 1262.0080000000003ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.392Z - Time taken for '@nx/js/typescript:createDependencies' 8.852600000000166ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.523Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.526Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.527Z - Time taken for 'total for creating and serializing project graph' 4276.733700000001ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.527Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.528Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 4277. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.534Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.536Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.537Z - Time taken for 'total for creating and serializing project graph' 1.2201999999997497ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.538Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.538Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.548Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.551Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.551Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:31.551Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:44.628Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:44.629Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:44.630Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:44.633Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:44.635Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:44.636Z - Time taken for 'total for creating and serializing project graph' 1.694500000001426ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:44.639Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:44.639Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 4.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:30:44.651Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.287Z - [WATCHER]: frontend was deleted
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.288Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.430Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.430Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.430Z - [REQUEST]: frontend
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.436Z - Time taken for 'hash changed files from watcher' 16.63729999998759ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.449Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.449Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.449Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.509Z - Time taken for 'build-project-configs' 61.607499999998254ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.511Z - Time taken for '@nx/js/typescript:createDependencies' 10.623000000006869ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.612Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.614Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:56.614Z - Time taken for 'total execution time for createProjectGraph()' 100.2100999999966ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.247Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.248Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.249Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.249Z - Established a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.250Z - Closed a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.251Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.253Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.254Z - Time taken for 'total for creating and serializing project graph' 1.0905000000057044ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.255Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.255Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.265Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.265Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.266Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:31:57.267Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.330Z - [WATCHER]: package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.330Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.442Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.442Z - [REQUEST]: package.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.442Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.456Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.457Z - Time taken for 'hash changed files from watcher' 0.6483000000007451ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.457Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.457Z - Handled HASH_GLOB. Handling time: 6. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.484Z - Time taken for 'build-project-configs' 26.53530000001774ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.486Z - Time taken for '@nx/js/typescript:createDependencies' 9.028800000000047ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.534Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.535Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.535Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:38.536Z - Time taken for 'total execution time for createProjectGraph()' 42.765299999999115ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:39.168Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:39.172Z - [WATCHER]: Stopping the watcher for d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (sources)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:39.172Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:39.173Z - [WATCHER]: Stopping the watcher for d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (outputs)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:39.178Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:43.474Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\f3183296a9513023b006\d.sock
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:43.478Z - [WATCHER]: Subscribed to changes within: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (native)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:43.482Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:43.490Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:43.519Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:43.520Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:43.521Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:43.523Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:44.687Z - Time taken for 'Load Nx Plugin: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1153.0891000000001ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:44.720Z - Time taken for 'Load Nx Plugin: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\package-json' 1188.0427ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:45.258Z - Time taken for 'loadDefaultNxPlugins' 1727.8959000000002ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:45.890Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 2364.6423ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:46.670Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:46.670Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:46.670Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:46.687Z - Time taken for 'loadSpecifiedNxPlugins' 3144.3492ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:46.694Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:46.695Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:46.696Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:46.700Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:46.701Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:46.701Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.490Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.490Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.490Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.547Z - Time taken for 'build-project-configs' 2840.9318000000003ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.550Z - Time taken for '@nx/js/typescript:createDependencies' 7.716699999999946ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.697Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.699Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.700Z - Time taken for 'total for creating and serializing project graph' 6175.7435ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.701Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.701Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 6176. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.708Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.708Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.709Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:33:49.709Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:19.182Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:19.183Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:19.184Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:19.187Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:19.188Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:19.189Z - Time taken for 'total for creating and serializing project graph' 0.8144999999931315ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:19.190Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:19.190Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:39.440Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:39.441Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:39.442Z - Time taken for 'total for creating and serializing project graph' 1.2774999999965075ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:39.447Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:39.447Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 6.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:39.463Z - [REQUEST]: Responding to the client. handleMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:39.463Z - Done responding to the client handleMultiGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:39.463Z - Handled MULTI_GLOB. Handling time: 1. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:39.693Z - [WATCHER]: 0 file(s) created or restored, 20 file(s) modified, 0 file(s) deleted
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:39.697Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:39.886Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:40.718Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:40.718Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:40.719Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:40.719Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:40.720Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:40.723Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:42.420Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 1695.4248999999982ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.184Z - Time taken for 'Load Nx Plugin: @nx/webpack/plugin' 2457.780199999994ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.284Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.284Z - [REQUEST]: package.json,backend-e2e/src/support/global-setup.ts,backend/tsconfig.json,backend-e2e/jest.config.ts,backend/src/app/app.service.ts,backend/src/app/app.controller.ts,backend/src/main.ts,backend/package.json,backend-e2e/src/support/test-setup.ts,nx.json,backend-e2e/src/support/global-teardown.ts,backend/webpack.config.js,backend-e2e/src/backend/backend.spec.ts,tsconfig.json,backend-e2e/.spec.swcrc,backend-e2e/tsconfig.json,backend/tsconfig.app.json,backend/src/assets/.gitkeep,backend-e2e/package.json,backend/src/app/app.module.ts
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.284Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.309Z - Time taken for 'loadSpecifiedNxPlugins' 2538.596099999995ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.311Z - Established a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.311Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.311Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.312Z - Established a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.313Z - Closed a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.313Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.319Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.326Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.334Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.334Z - Handled HASH_GLOB. Handling time: 2. Response time: 15.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.354Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.355Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.355Z - Handled HASH_GLOB. Handling time: 3. Response time: 29.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.356Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:43.356Z - Handled HASH_GLOB. Handling time: 18. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.133Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 3409.046900000001ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.288Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.289Z - [REQUEST]: package.json,backend-e2e/src/support/global-setup.ts,backend/tsconfig.json,backend-e2e/jest.config.ts,backend/src/app/app.service.ts,backend/src/app/app.controller.ts,backend/src/main.ts,backend/package.json,backend-e2e/src/support/test-setup.ts,nx.json,backend-e2e/src/support/global-teardown.ts,backend/webpack.config.js,backend-e2e/src/backend/backend.spec.ts,tsconfig.json,backend-e2e/.spec.swcrc,backend-e2e/tsconfig.json,backend/tsconfig.app.json,backend/src/assets/.gitkeep,backend-e2e/package.json,backend/src/app/app.module.ts
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.289Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.296Z - Time taken for 'loadSpecifiedNxPlugins' 3530.5527ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.307Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.307Z - Established a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.307Z - Closed a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.334Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.340Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.340Z - Handled HASH_GLOB. Handling time: 24. Response time: 6.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.349Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.351Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:44.351Z - Handled HASH_GLOB. Handling time: 7. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:46.315Z - Time taken for 'build-project-configs' 2008.983500000002ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:46.320Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:46.322Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:46.322Z - Handled HASH_GLOB. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:46.322Z - Time taken for '@nx/js/typescript:createDependencies' 13.87060000000929ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:46.342Z - Time taken for 'build-project-configs' 2033.4513000000006ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:46.344Z - Time taken for '@nx/js/typescript:createDependencies' 11.940199999997276ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:46.953Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:46.955Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:46.955Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:46.956Z - Time taken for 'total execution time for createProjectGraph()' 603.3842000000004ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:47.016Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:47.017Z - Time taken for 'total for creating and serializing project graph' 6293.423800000004ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:47.018Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:47.018Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 6293. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:47.029Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:47.029Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:47.030Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:47.030Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:47.063Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:48.051Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:48.052Z - Established a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:48.052Z - Closed a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:48.055Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:48.056Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:48.057Z - Time taken for 'total for creating and serializing project graph' 1.1325999999971827ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:48.189Z - [SYNC]: @nx/js:typescript-sync changes: tsconfig.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:48.189Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 1124.9103000000032ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:48.189Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:35:48.189Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 133.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:02.221Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:02.222Z - [WATCHER]: Stopping the watcher for d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (sources)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:02.223Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:02.224Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:02.224Z - Closed a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:02.224Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:02.224Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:02.225Z - [WATCHER]: Stopping the watcher for d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (outputs)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:02.228Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:05.765Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\f3183296a9513023b006\d.sock
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:05.770Z - [WATCHER]: Subscribed to changes within: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (native)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:05.776Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:05.782Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:05.874Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:05.875Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:05.941Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:05.949Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:07.423Z - Time taken for 'Load Nx Plugin: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1527.8021000000003ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:07.595Z - Time taken for 'Load Nx Plugin: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\js' 1705.3876999999998ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:07.632Z - Time taken for 'loadDefaultNxPlugins' 1742.8089ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.099Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 2221.4347000000002ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.241Z - Time taken for 'Load Nx Plugin: @nx/next/plugin' 2359.0442999999996ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.333Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.334Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.334Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.349Z - Time taken for 'loadSpecifiedNxPlugins' 2455.6496ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.370Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.371Z - Established a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.371Z - Closed a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.377Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.378Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.378Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.394Z - Established a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.403Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.404Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.417Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.418Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:08.418Z - Handled HASH_GLOB. Handling time: 10. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:09.539Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:09.540Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:09.540Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:10.339Z - Time taken for 'build-project-configs' 1970.2790000000005ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:10.341Z - Time taken for '@nx/js/typescript:createDependencies' 8.079599999999118ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:10.479Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:10.482Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:10.482Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:10.483Z - Time taken for 'total for creating and serializing project graph' 4533.2887ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:10.485Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:10.486Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 4533. Response time: 4.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:10.493Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:10.494Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:10.494Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:10.494Z - Closed a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:10.584Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:11.420Z - Established a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:11.420Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:11.420Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:11.423Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:11.424Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:11.424Z - Time taken for 'total for creating and serializing project graph' 0.9080000000003565ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:11.518Z - [SYNC]: @nx/js:typescript-sync changes: tsconfig.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:11.518Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 933.2459999999992ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:11.518Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:36:11.518Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 94.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.542Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.542Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.543Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.553Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.555Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.556Z - Time taken for 'total for creating and serializing project graph' 1.5861999999979162ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.558Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.558Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.570Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.571Z - Time taken for 'preTasksExecution' 0.6922000000049593ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.571Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.571Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.596Z - [SYNC]: get sync generators changes on demand ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.597Z - [SYNC]: @nx/js:typescript-sync not scheduled and has cached result, returning cached result
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.597Z - [REQUEST]: Responding to the client. handleGetSyncGeneratorChanges
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.597Z - Done responding to the client handleGetSyncGeneratorChanges
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.598Z - Handled GET_SYNC_GENERATOR_CHANGES. Handling time: 1. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.653Z - [SYNC]: flush sync generators changes ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.653Z - [SYNC]: get sync generators changes on demand ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.653Z - [SYNC]: @nx/js:typescript-sync not scheduled and has cached result, returning cached result
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.657Z - [REQUEST]: Responding to the client. handleFlushSyncGeneratorChangesToDisk
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.657Z - Time taken for 'flush sync generator changes to disk' 3.1569000000017695ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.658Z - Done responding to the client handleFlushSyncGeneratorChangesToDisk
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.658Z - Handled CLEAR_CACHED_SYNC_GENERATOR_CHANGES. Handling time: 4. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.660Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.662Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.662Z - [REQUEST]: tsconfig.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.662Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.683Z - Time taken for 'hash changed files from watcher' 0.5097999999998137ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.689Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.696Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.698Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.698Z - Handled HASH_GLOB. Handling time: 4. Response time: 9.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.700Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.701Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.701Z - Handled HASH_GLOB. Handling time: 1. Response time: 5.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.706Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.706Z - Handled HASH_GLOB. Handling time: 0. Response time: 6.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.717Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.720Z - [WATCHER]: tsconfig.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.734Z - Time taken for 'build-project-configs' 58.12410000000091ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.737Z - Time taken for '@nx/js/typescript:createDependencies' 9.47559999999794ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.790Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.791Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.791Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.791Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.791Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.793Z - Time taken for 'total for creating and serializing project graph' 131.02350000000297ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.794Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.794Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 131. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.829Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.829Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.829Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.851Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.852Z - Time taken for 'hash changed files from watcher' 0.15639999999984866ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.858Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.859Z - Handled HASH_GLOB. Handling time: 3. Response time: 8.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.874Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.875Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.875Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.879Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.879Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.879Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.896Z - Time taken for 'build-project-configs' 52.22729999999865ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.897Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.910Z - Time taken for '@nx/js/typescript:createDependencies' 20.978699999999662ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.914Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.952Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.954Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.954Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.954Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.955Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:00.956Z - Time taken for 'total execution time for createProjectGraph()' 50.49289999999746ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.051Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.051Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 153.5731999999989ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.051Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.051Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 41. Response time: 96.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.161Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.169Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.170Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.171Z - Time taken for 'total for creating and serializing project graph' 0.7590999999956694ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.207Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.207Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 45.06899999999587ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.208Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.208Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 38.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.249Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.250Z - Done responding to the client handleHashTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.251Z - Handled HASH_TASKS. Handling time: 39. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.328Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.329Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.330Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 13. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.729Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.729Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.730Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.730Z - Established a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.731Z - Closed a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.733Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.735Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.736Z - Time taken for 'total for creating and serializing project graph' 1.1995999999999185ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.736Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.737Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.745Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.746Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.746Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:01.747Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:16.876Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:16.877Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:16.877Z - Done responding to the client recordOutputsHash
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:16.877Z - Handled RECORD_OUTPUTS_HASH. Handling time: 13. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:18.283Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:18.286Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:18.293Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:18.297Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:18.298Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:18.299Z - Time taken for 'total for creating and serializing project graph' 1.3585999999922933ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:18.302Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:18.302Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:18.310Z - Established a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.368Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.368Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.369Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.374Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.376Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.377Z - Time taken for 'total for creating and serializing project graph' 1.6808000000019092ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.384Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.385Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 9.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.403Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.403Z - Time taken for 'preTasksExecution' 0.692300000009709ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.404Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.404Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.411Z - [SYNC]: get sync generators changes on demand ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.411Z - [SYNC]: @nx/js:typescript-sync not scheduled and has cached result, returning cached result
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.411Z - [REQUEST]: Responding to the client. handleGetSyncGeneratorChanges
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.412Z - Done responding to the client handleGetSyncGeneratorChanges
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.412Z - Handled GET_SYNC_GENERATOR_CHANGES. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.494Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.495Z - Done responding to the client handleHashTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.495Z - Handled HASH_TASKS. Handling time: 11. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.554Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.554Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:20.554Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:25.167Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.338Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.349Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.365Z - Done responding to the client recordOutputsHash
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.365Z - Handled RECORD_OUTPUTS_HASH. Handling time: 8. Response time: 16.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.448Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.449Z - Done responding to the client handleRecordTaskRuns
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.449Z - Handled RECORD_TASK_RUNS. Handling time: 24. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.451Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.452Z - Done responding to the client handleGetFlakyTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.452Z - Handled GET_FLAKY_TASKS. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.463Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.464Z - Time taken for 'postTasksExecution' 1.1612999999924796ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.464Z - Done responding to the client handleRunPostTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.465Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:37:26.469Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.469Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.472Z - [WATCHER]: backend was deleted
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.473Z - Time taken for 'changed-projects' 0.14120000001275912ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.578Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.579Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.579Z - [REQUEST]: backend
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.596Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.596Z - Time taken for 'hash changed files from watcher' 0.1631000000052154ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.598Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.598Z - Handled HASH_GLOB. Handling time: 5. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.602Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.608Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.608Z - Handled HASH_GLOB. Handling time: 2. Response time: 6.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.624Z - Time taken for 'build-project-configs' 30.109599999996135ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.625Z - Time taken for '@nx/js/typescript:createDependencies' 6.815999999991618ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.691Z - Error detected when creating a project graph: Failed to create project graph. See above for errors
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:07.695Z - Time taken for 'createDependencies' 72.50849999999627ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:08.483Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:08.484Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:08.485Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:08.485Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:08.485Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:08.488Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:08.491Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Failed to process project graph.
    WorkspaceValidityError: Configuration Error
  The following implicitDependencies point to non-existent project(s):
    @nurse-platform/backend-e2e
      @nurse-platform/backend
      at assertWorkspaceValidity (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\assert-workspace-validity.js:71:11)
      at buildProjectGraphUsingProjectFileMap (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\project-graph\build-project-graph.js:62:65)
      at createAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:241:124)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async processFilesAndCreateAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:172:19)
      at async Timeout._onTimeout (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:94:38)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:08.493Z - Done responding to the client null
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:08.493Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:08.496Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:08.497Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:08.497Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:08.498Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:09.730Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:09.730Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:09.731Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:09.731Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:09.731Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:09.733Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:09.734Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Failed to process project graph.
    WorkspaceValidityError: Configuration Error
  The following implicitDependencies point to non-existent project(s):
    @nurse-platform/backend-e2e
      @nurse-platform/backend
      at assertWorkspaceValidity (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\assert-workspace-validity.js:71:11)
      at buildProjectGraphUsingProjectFileMap (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\project-graph\build-project-graph.js:62:65)
      at createAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:241:124)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async processFilesAndCreateAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:172:19)
      at async Timeout._onTimeout (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:94:38)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:09.735Z - Done responding to the client null
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:09.735Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:09.737Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:09.737Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:09.737Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 1. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:09.738Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:13.957Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:13.957Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:13.958Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:13.958Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:13.958Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:13.960Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:13.961Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Failed to process project graph.
    WorkspaceValidityError: Configuration Error
  The following implicitDependencies point to non-existent project(s):
    @nurse-platform/backend-e2e
      @nurse-platform/backend
      at assertWorkspaceValidity (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\assert-workspace-validity.js:71:11)
      at buildProjectGraphUsingProjectFileMap (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\project-graph\build-project-graph.js:62:65)
      at createAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:241:124)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async processFilesAndCreateAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:172:19)
      at async Timeout._onTimeout (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:94:38)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:13.962Z - Done responding to the client null
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:13.962Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:13.964Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:13.964Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:13.965Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:13.965Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.547Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.548Z - [WATCHER]: apps/frontend/backend was deleted
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.549Z - Time taken for 'changed-projects' 0.23339999999734573ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.651Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.651Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.651Z - [REQUEST]: apps/frontend/backend
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.678Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.679Z - Time taken for 'hash changed files from watcher' 0.1040999999968335ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.688Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.688Z - Handled HASH_GLOB. Handling time: 8. Response time: 10.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.691Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.692Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.692Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.708Z - Time taken for 'build-project-configs' 42.75649999998859ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.710Z - Time taken for '@nx/js/typescript:createDependencies' 8.405100000003586ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.757Z - Error detected when creating a project graph: Failed to create project graph. See above for errors
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:18.762Z - Time taken for 'createDependencies' 56.14449999999488ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:19.560Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:19.561Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:19.561Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:19.562Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:19.562Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:19.563Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:19.564Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Failed to process project graph.
    WorkspaceValidityError: Configuration Error
  The following implicitDependencies point to non-existent project(s):
    @nurse-platform/backend-e2e
      @nurse-platform/backend
      at assertWorkspaceValidity (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\assert-workspace-validity.js:71:11)
      at buildProjectGraphUsingProjectFileMap (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\project-graph\build-project-graph.js:62:65)
      at createAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:241:124)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async processFilesAndCreateAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:172:19)
      at async Timeout._onTimeout (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:94:38)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:19.565Z - Done responding to the client null
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:19.565Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:19.567Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:19.568Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:19.568Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:19.568Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:23.127Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:23.128Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:23.129Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:23.129Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:23.129Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:23.132Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:23.133Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Failed to process project graph.
    WorkspaceValidityError: Configuration Error
  The following implicitDependencies point to non-existent project(s):
    @nurse-platform/backend-e2e
      @nurse-platform/backend
      at assertWorkspaceValidity (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\assert-workspace-validity.js:71:11)
      at buildProjectGraphUsingProjectFileMap (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\project-graph\build-project-graph.js:62:65)
      at createAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:241:124)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async processFilesAndCreateAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:172:19)
      at async Timeout._onTimeout (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:94:38)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:23.134Z - Done responding to the client null
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:23.135Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:23.137Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:23.138Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:23.138Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:23.139Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:24.326Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:24.326Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:24.326Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:24.327Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:24.327Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:24.328Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:24.329Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Failed to process project graph.
    WorkspaceValidityError: Configuration Error
  The following implicitDependencies point to non-existent project(s):
    @nurse-platform/backend-e2e
      @nurse-platform/backend
      at assertWorkspaceValidity (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\assert-workspace-validity.js:71:11)
      at buildProjectGraphUsingProjectFileMap (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\project-graph\build-project-graph.js:62:65)
      at createAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:241:124)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async processFilesAndCreateAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:172:19)
      at async Timeout._onTimeout (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:94:38)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:24.330Z - Done responding to the client null
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:24.330Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:24.332Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:24.333Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:24.333Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:24.333Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:28.609Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:28.612Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:28.613Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:28.613Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:28.614Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:28.616Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:28.618Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Failed to process project graph.
    WorkspaceValidityError: Configuration Error
  The following implicitDependencies point to non-existent project(s):
    @nurse-platform/backend-e2e
      @nurse-platform/backend
      at assertWorkspaceValidity (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\assert-workspace-validity.js:71:11)
      at buildProjectGraphUsingProjectFileMap (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\project-graph\build-project-graph.js:62:65)
      at createAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:241:124)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async processFilesAndCreateAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:172:19)
      at async Timeout._onTimeout (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:94:38)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:28.619Z - Done responding to the client null
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:28.619Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:28.622Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:28.623Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:28.623Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:28.623Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:38.327Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:38.328Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:38.328Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:38.328Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:38.329Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:38.331Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:38.332Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Failed to process project graph.
    WorkspaceValidityError: Configuration Error
  The following implicitDependencies point to non-existent project(s):
    @nurse-platform/backend-e2e
      @nurse-platform/backend
      at assertWorkspaceValidity (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\assert-workspace-validity.js:71:11)
      at buildProjectGraphUsingProjectFileMap (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\project-graph\build-project-graph.js:62:65)
      at createAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:241:124)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async processFilesAndCreateAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:172:19)
      at async Timeout._onTimeout (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:94:38)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:38.333Z - Done responding to the client null
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:38.333Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:38.337Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:38.338Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:38.338Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:39:38.338Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:40:48.432Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:40:48.433Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:40:48.434Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:40:48.489Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:40:48.490Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Failed to process project graph.
    WorkspaceValidityError: Configuration Error
  The following implicitDependencies point to non-existent project(s):
    @nurse-platform/backend-e2e
      @nurse-platform/backend
      at assertWorkspaceValidity (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\assert-workspace-validity.js:71:11)
      at buildProjectGraphUsingProjectFileMap (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\project-graph\build-project-graph.js:62:65)
      at createAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:241:124)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5)
      at async processFilesAndCreateAndSerializeProjectGraph (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:172:19)
      at async Timeout._onTimeout (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:94:38)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:40:48.491Z - Done responding to the client null
[NX v21.2.1 Daemon Server] - 2025-06-24T11:40:48.491Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:40:48.495Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:15.990Z - [WATCHER]: backend-e2e was deleted
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:15.991Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:15.991Z - Time taken for 'changed-projects' 0.23599999997531995ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.106Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.106Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.106Z - [REQUEST]: backend-e2e
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.110Z - Time taken for 'hash changed files from watcher' 0.1603999999933876ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.176Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.176Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.177Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.179Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.180Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.180Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.290Z - Time taken for 'build-project-configs' 142.0332000000053ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.296Z - Time taken for '@nx/js/typescript:createDependencies' 20.440300000016578ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.426Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.428Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:16.428Z - Time taken for 'total execution time for createProjectGraph()' 133.92910000000848ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.002Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.007Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.008Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.008Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.009Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.011Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.013Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.014Z - Time taken for 'total for creating and serializing project graph' 1.274600000004284ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.014Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.014Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.024Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.025Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.025Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:17.026Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.030Z - [WATCHER]: apps/backend/tsconfig.app.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.031Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.031Z - Time taken for 'changed-projects' 0.19139999995240942ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.140Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.140Z - [REQUEST]: apps/backend/tsconfig.app.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.140Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.160Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.161Z - Time taken for 'hash changed files from watcher' 0.38459999999031425ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.162Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.162Z - Handled HASH_GLOB. Handling time: 8. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.170Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.177Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.177Z - Handled HASH_GLOB. Handling time: 6. Response time: 7.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.216Z - Time taken for 'build-project-configs' 51.3859999999986ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.217Z - Time taken for '@nx/js/typescript:createDependencies' 14.913800000038464ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.285Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.287Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:27.287Z - Time taken for 'total execution time for createProjectGraph()' 63.397500000020955ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.045Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.046Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.046Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.047Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.047Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.049Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.051Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.052Z - Time taken for 'total for creating and serializing project graph' 1.4451999999582767ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.053Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.053Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.062Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.063Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.063Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:28.063Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.179Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.216Z - [WATCHER]: apps/backend/tsconfig.app.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.232Z - [REQUEST]: Responding to the client. File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.232Z - Time taken for 'changed-projects' 0.13750000001164153ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.258Z - Done responding to the client File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.258Z - Handled FILE-WATCH-CHANGED. Handling time: 1. Response time: 26.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.347Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.348Z - [REQUEST]: apps/backend/tsconfig.app.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.348Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.405Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.406Z - Time taken for 'hash changed files from watcher' 0.7962999999872409ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.430Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.430Z - Handled HASH_GLOB. Handling time: 21. Response time: 25.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.434Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.435Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.435Z - Handled HASH_GLOB. Handling time: 2. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.457Z - Time taken for 'build-project-configs' 73.93569999997271ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.458Z - Time taken for '@nx/js/typescript:createDependencies' 12.260000000009313ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.534Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.535Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.536Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:29.536Z - Time taken for 'total execution time for createProjectGraph()' 68.83079999999609ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.254Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.254Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.255Z - Established a connection. Number of open connections: 9
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.255Z - Closed a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.255Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.257Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.259Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.262Z - Time taken for 'total for creating and serializing project graph' 1.2164000000339001ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.262Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.262Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.295Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.297Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.298Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:30.299Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:32.924Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:32.925Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:32.927Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:32.933Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:32.935Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:32.936Z - Time taken for 'total for creating and serializing project graph' 1.1485000000102445ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:32.939Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:32.939Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 4.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:32.957Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:32.957Z - Time taken for 'preTasksExecution' 1.211699999985285ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:32.958Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:32.958Z - Handled PRE_TASKS_EXECUTION. Handling time: 2. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:32.964Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.688Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.693Z - [WATCHER]: apps/backend/tsconfig.app.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.694Z - [REQUEST]: Responding to the client. File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.695Z - Time taken for 'changed-projects' 0.12280000001192093ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.696Z - Done responding to the client File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.696Z - Handled FILE-WATCH-CHANGED. Handling time: 0. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.804Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.805Z - [REQUEST]: apps/backend/tsconfig.app.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.805Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.827Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.827Z - Time taken for 'hash changed files from watcher' 0.3520000000135042ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.828Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.828Z - Handled HASH_GLOB. Handling time: 3. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.836Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.842Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.843Z - Handled HASH_GLOB. Handling time: 5. Response time: 8.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.862Z - Time taken for 'build-project-configs' 35.26240000000689ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.864Z - Time taken for '@nx/js/typescript:createDependencies' 10.371600000013132ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.930Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.932Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.932Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:45.933Z - Time taken for 'total execution time for createProjectGraph()' 57.9381000000285ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.704Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.704Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.705Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.706Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.706Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.708Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.709Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.712Z - Time taken for 'total for creating and serializing project graph' 1.2387999999918975ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.712Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.713Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.722Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.723Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.723Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:46.724Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.284Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.286Z - [WATCHER]: apps/backend/tsconfig.app.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.287Z - [REQUEST]: Responding to the client. File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.288Z - Time taken for 'changed-projects' 0.05869999999413267ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.288Z - Done responding to the client File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.288Z - Handled FILE-WATCH-CHANGED. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.397Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.398Z - [REQUEST]: apps/backend/tsconfig.app.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.398Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.440Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.442Z - Time taken for 'hash changed files from watcher' 0.33100000000558794ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.449Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.449Z - Handled HASH_GLOB. Handling time: 11. Response time: 9.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.462Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.463Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.463Z - Handled HASH_GLOB. Handling time: 11. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.482Z - Time taken for 'build-project-configs' 51.400599999993574ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.483Z - Time taken for '@nx/js/typescript:createDependencies' 10.579200000036508ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.555Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.557Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.557Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:47.557Z - Time taken for 'total execution time for createProjectGraph()' 59.553100000019185ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.342Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.342Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.343Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.344Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.344Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.347Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.348Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.350Z - Time taken for 'total for creating and serializing project graph' 1.5554999999585561ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.351Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.351Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.360Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.360Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.360Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:48.361Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:49.573Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:49.574Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:49.576Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:49.582Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:49.584Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:49.585Z - Time taken for 'total for creating and serializing project graph' 1.1830000000190921ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:49.593Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:49.593Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 9.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:49.624Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:49.624Z - Time taken for 'preTasksExecution' 1.0738999999593943ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:49.625Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:49.625Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:49.631Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.139Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.147Z - [WATCHER]: apps/backend/tsconfig.app.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.148Z - [REQUEST]: Responding to the client. File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.148Z - Time taken for 'changed-projects' 0.055500000016763806ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.148Z - Done responding to the client File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.148Z - Handled FILE-WATCH-CHANGED. Handling time: 0. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.256Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.256Z - [REQUEST]: apps/backend/tsconfig.app.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.256Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.302Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.303Z - Time taken for 'hash changed files from watcher' 0.3229999999748543ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.343Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.343Z - Handled HASH_GLOB. Handling time: 12. Response time: 41.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.351Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.353Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.353Z - Handled HASH_GLOB. Handling time: 6. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.404Z - Time taken for 'build-project-configs' 78.1140000000014ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.407Z - Time taken for '@nx/js/typescript:createDependencies' 9.901300000026822ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:50.555Z - Time taken for 'total execution time for createProjectGraph()' 115.29529999999795ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.268Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.285Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.285Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.286Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.287Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.289Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.290Z - [REQUEST]: Responding to the client with an error. Error when preparing serialized project graph. Failed to process project graph.
     - Error: Debug Failure. Expected d:/Intake45_ITI_9-months_Open_Source/Graduation-Project-ITI/nurse-platform/apps/backend/tsconfig.app.json === d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\apps\backend\tsconfig.app.json. 
      at attachFileToDiagnostic (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\typescript\lib\typescript.js:21598:9)
      at attachFileToDiagnostics (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\typescript\lib\typescript.js:21627:34)
      at Object.parseJsonText2 [as parseJsonText] (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\typescript\lib\typescript.js:33176:35)
      at parseJsonText (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\typescript\lib\typescript.js:32933:17)
      at parseConfigFileTextToJson (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\typescript\lib\typescript.js:42320:26)
      at Object.readConfigFile (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\typescript\lib\typescript.js:42317:39)
      at readTsConfig (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\@nx\js\src\plugins\typescript\plugin.js:697:27)
      at readTsConfigAndCache (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\@nx\js\src\plugins\typescript\plugin.js:645:22)
      at initializeTsConfigCache (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\@nx\js\src\plugins\typescript\plugin.js:631:9)
      at exports.createNodesV2 (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\@nx\js\src\plugins\typescript\plugin.js:78:9)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.298Z - Done responding to the client null
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.298Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 8.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.307Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.308Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.308Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.309Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.658Z - [WATCHER]: apps/backend/tsconfig.app.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.659Z - Time taken for 'changed-projects' 0.14279999997233972ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.662Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.760Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.761Z - [REQUEST]: apps/backend/tsconfig.app.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.761Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.818Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.818Z - Time taken for 'hash changed files from watcher' 0.3883999999961816ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.824Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.824Z - Handled HASH_GLOB. Handling time: 18. Response time: 6.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.834Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.834Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.834Z - Handled HASH_GLOB. Handling time: 7. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.865Z - Time taken for 'build-project-configs' 51.05800000001909ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.866Z - Time taken for '@nx/js/typescript:createDependencies' 9.637899999972433ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.934Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.936Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.936Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:51.937Z - Time taken for 'total execution time for createProjectGraph()' 55.522999999986496ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.688Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.688Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.689Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.689Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.690Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.692Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.694Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.695Z - Time taken for 'total for creating and serializing project graph' 1.2815999999875203ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.696Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.696Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.704Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.705Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.705Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.706Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.871Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.872Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.874Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.882Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.883Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.884Z - Time taken for 'total for creating and serializing project graph' 1.1087999999872409ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.916Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.917Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 34.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.960Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.960Z - Time taken for 'preTasksExecution' 1.0344000000040978ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.961Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.961Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:52.965Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.323Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.324Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.325Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.330Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.332Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.333Z - Time taken for 'total for creating and serializing project graph' 1.1995999999926426ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.337Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.337Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 6.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.352Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.353Z - Time taken for 'preTasksExecution' 0.9827999999979511ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.353Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.353Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.357Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.514Z - [WATCHER]: apps/backend/tsconfig.app.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.515Z - [REQUEST]: Responding to the client. File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.515Z - Time taken for 'changed-projects' 0.11180000001331791ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.516Z - Done responding to the client File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.516Z - Handled FILE-WATCH-CHANGED. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.520Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.620Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.620Z - [REQUEST]: apps/backend/tsconfig.app.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.620Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.645Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.646Z - Time taken for 'hash changed files from watcher' 0.32400000002235174ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.652Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.652Z - Handled HASH_GLOB. Handling time: 13. Response time: 7.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.657Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.665Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.665Z - Handled HASH_GLOB. Handling time: 3. Response time: 8.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.692Z - Time taken for 'build-project-configs' 47.94429999997374ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.693Z - Time taken for '@nx/js/typescript:createDependencies' 18.07660000002943ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.759Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.760Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.761Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:54.761Z - Time taken for 'total execution time for createProjectGraph()' 62.64439999999013ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.526Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.527Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.528Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.528Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.529Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.531Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.532Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.533Z - Time taken for 'total for creating and serializing project graph' 1.1508000000030734ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.534Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.534Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.543Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.544Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.544Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:55.544Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.353Z - [WATCHER]: apps/backend/tsconfig.app.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.354Z - [REQUEST]: Responding to the client. File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.358Z - Time taken for 'changed-projects' 0.07000000000698492ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.359Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.359Z - Done responding to the client File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.359Z - Handled FILE-WATCH-CHANGED. Handling time: 0. Response time: 5.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.438Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.439Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.440Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.450Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.451Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.452Z - [REQUEST]: apps/backend/tsconfig.app.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.452Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.470Z - Time taken for 'hash changed files from watcher' 0.3361999999615364ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.490Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.496Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.496Z - Handled HASH_GLOB. Handling time: 13. Response time: 6.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.505Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.506Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.506Z - Handled HASH_GLOB. Handling time: 4. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.523Z - Time taken for 'build-project-configs' 42.49829999997746ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.525Z - Time taken for '@nx/js/typescript:createDependencies' 10.258899999957066ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.592Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.593Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.594Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.594Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.595Z - Time taken for 'total for creating and serializing project graph' 144.13689999998314ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.598Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.599Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 144. Response time: 5.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.616Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.616Z - Time taken for 'preTasksExecution' 0.9866999999503605ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.617Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.617Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:57.621Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.390Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.391Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.391Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.392Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.392Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.394Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.396Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.397Z - Time taken for 'total for creating and serializing project graph' 1.28259999997681ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.397Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.398Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.406Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.407Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.407Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:58.408Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:59.911Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:59.912Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:59.913Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:59.918Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:59.919Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:59.920Z - Time taken for 'total for creating and serializing project graph' 1.2016999999759719ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:59.925Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:59.925Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 6.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:59.938Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:59.939Z - Time taken for 'preTasksExecution' 0.8018000000156462ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:59.939Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:59.939Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:41:59.943Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.633Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.635Z - [WATCHER]: apps/backend/tsconfig.app.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.636Z - [REQUEST]: Responding to the client. File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.636Z - Time taken for 'changed-projects' 0.06680000002961606ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.636Z - Done responding to the client File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.636Z - Handled FILE-WATCH-CHANGED. Handling time: 0. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.742Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.742Z - [REQUEST]: apps/backend/tsconfig.app.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.742Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.784Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.785Z - Time taken for 'hash changed files from watcher' 0.3267999999807216ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.791Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.791Z - Handled HASH_GLOB. Handling time: 13. Response time: 7.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.794Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.794Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.795Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.812Z - Time taken for 'build-project-configs' 38.64389999996638ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.813Z - Time taken for '@nx/js/typescript:createDependencies' 9.684799999988172ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.875Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.876Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.877Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:00.877Z - Time taken for 'total execution time for createProjectGraph()' 51.34429999999702ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.653Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.653Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.653Z - Established a connection. Number of open connections: 9
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.654Z - Closed a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.654Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.656Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.657Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.659Z - Time taken for 'total for creating and serializing project graph' 1.116600000008475ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.661Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.663Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 6.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.668Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.669Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.669Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:01.670Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:03.798Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:03.798Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:03.799Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:03.805Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:03.806Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:03.807Z - Time taken for 'total for creating and serializing project graph' 1.1050999999861233ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:03.810Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:03.810Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:03.825Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:03.826Z - Time taken for 'preTasksExecution' 0.8632999999681488ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:03.826Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:03.827Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:03.830Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.827Z - [WATCHER]: apps/backend/tsconfig.app.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.828Z - [REQUEST]: Responding to the client. File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.829Z - Time taken for 'changed-projects' 0.08250000001862645ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.829Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.830Z - Done responding to the client File watch changed
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.830Z - Handled FILE-WATCH-CHANGED. Handling time: 0. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.938Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.938Z - [REQUEST]: apps/backend/tsconfig.app.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.938Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.970Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.970Z - Time taken for 'hash changed files from watcher' 0.39870000001974404ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.971Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.971Z - Handled HASH_GLOB. Handling time: 10. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.980Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.980Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:04.980Z - Handled HASH_GLOB. Handling time: 6. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.013Z - Time taken for 'build-project-configs' 51.55169999995269ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.014Z - Time taken for '@nx/js/typescript:createDependencies' 9.941099999996368ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.087Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.090Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.090Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.090Z - Time taken for 'total execution time for createProjectGraph()' 54.4153000000515ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.828Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.828Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.829Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.829Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.829Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.832Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.833Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.834Z - Time taken for 'total for creating and serializing project graph' 1.4642999999923632ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.835Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.835Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.844Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.845Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.845Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:05.846Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:07.492Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:07.494Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:07.495Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:07.500Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:07.501Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:07.502Z - Time taken for 'total for creating and serializing project graph' 1.1952000000164844ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:07.507Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:07.507Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 6.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:07.522Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:07.522Z - Time taken for 'preTasksExecution' 0.9377000000094995ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:07.523Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:07.523Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:42:07.527Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:43:23.140Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:43:23.141Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:43:23.142Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:43:23.176Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:43:23.177Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:43:23.177Z - Time taken for 'total for creating and serializing project graph' 0.9647000000113621ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:43:23.179Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:43:23.180Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:43:23.314Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.368Z - [WATCHER]: nx.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.369Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.369Z - Time taken for 'changed-projects' 0.0506000000750646ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.477Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.477Z - [REQUEST]: nx.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.477Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.487Z - Time taken for 'hash changed files from watcher' 0.3782000000355765ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.526Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.526Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.526Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.528Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.529Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.529Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.648Z - Time taken for 'build-project-configs' 155.92470000009052ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.649Z - Time taken for '@nx/js/typescript:createDependencies' 7.551800000015646ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.684Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.685Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.685Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:31.686Z - Time taken for 'total execution time for createProjectGraph()' 28.51799999992363ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.858Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.858Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.859Z - Established a connection. Number of open connections: 9
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.859Z - Closed a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.859Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.861Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.863Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.866Z - Time taken for 'total for creating and serializing project graph' 1.6515000000363216ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.867Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.867Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 4.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.875Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.875Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.876Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:32.876Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.821Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.832Z - [WATCHER]: nx.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.834Z - Time taken for 'changed-projects' 0.0590000000083819ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.949Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.950Z - [REQUEST]: nx.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.950Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.971Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.971Z - Time taken for 'hash changed files from watcher' 0.3216000000247732ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.974Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.974Z - Handled HASH_GLOB. Handling time: 8. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.976Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.983Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.983Z - Handled HASH_GLOB. Handling time: 0. Response time: 7.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:34.999Z - Time taken for 'build-project-configs' 33.41480000002775ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.001Z - Time taken for '@nx/js/typescript:createDependencies' 10.334499999997206ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.055Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.057Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.057Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.057Z - Time taken for 'total execution time for createProjectGraph()' 48.30870000005234ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.856Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.857Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.857Z - Established a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.858Z - Established a connection. Number of open connections: 8
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.858Z - Closed a connection. Number of open connections: 7
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.860Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.862Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.863Z - Time taken for 'total for creating and serializing project graph' 1.1868000000249594ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.864Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.864Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.871Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.871Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.871Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:35.872Z - Closed a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:38.812Z - [WATCHER]: nx.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:38.814Z - Time taken for 'changed-projects' 0.0501999999396503ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:38.820Z - [WATCHER]: Processing file changes in outputs
d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\json.js:29
        throw new Error(formatParseError(input, errors[0]));
              ^

Error: CommaExpected in d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\nx.json at 29:27
  27 |       }
  28 |     },
> 29 |     "implicitDependencies": {
     |                           ^
  30 |   "@nurse-platform/backend-e2e": ["@nurse-platform/backend"]
  31 | }
  32 | 

    at parseJson (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\json.js:29:15)
    at readJsonFile (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\fileutils.js:32:37)
    at readNxJson (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\config\nx-json.js:12:66)
    at getPlugins (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\project-graph\plugins\get-plugins.js:23:59)
    at Timeout._onTimeout (d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:93:98)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

Node.js v22.14.0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.158Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\f3183296a9513023b006\d.sock
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.170Z - [WATCHER]: Subscribed to changes within: D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (native)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.174Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.175Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.178Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.210Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.212Z - Done responding to the client handleRecordTaskRuns
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.212Z - Handled RECORD_TASK_RUNS. Handling time: 28. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.217Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.218Z - Done responding to the client handleGetFlakyTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.218Z - Handled GET_FLAKY_TASKS. Handling time: 2. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.231Z - [REQUEST]: Responding to the client with an error. Error when running postTasksExecution. CommaExpected in D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\nx.json at 29:27
  27 |       }
  28 |     },
> 29 |     "implicitDependencies": {
     |                           ^
  30 |   "@nurse-platform/backend-e2e": ["@nurse-platform/backend"]
  31 | },
  32 |     {

Error: CommaExpected in D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\nx.json at 29:27
  27 |       }
  28 |     },
> 29 |     "implicitDependencies": {
     |                           ^
  30 |   "@nurse-platform/backend-e2e": ["@nurse-platform/backend"]
  31 | },
  32 |     {

    at parseJson (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\json.js:29:15)
    at readJsonFile (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\fileutils.js:32:37)
    at readNxJson (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\config\nx-json.js:12:66)
    at getPlugins (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\project-graph\plugins\get-plugins.js:23:59)
    at runPostTasksExecution (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\project-graph\plugins\tasks-execution-hooks.js:46:60)
    at handleRunPostTasksExecution (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\handle-tasks-execution-hooks.js:23:65)
    at D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\server.js:178:155
    at handleResult (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\server.js:186:22)
    at handleMessage (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\server.js:178:15)
    at D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\server.js:74:15
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.232Z - Done responding to the client null
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.232Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:44.235Z - Closed a connection. Number of open connections: 0
D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\json.js:29
        throw new Error(formatParseError(input, errors[0]));
              ^

Error: CommaExpected in D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\nx.json at 29:27
  27 |       }
  28 |     },
> 29 |     "implicitDependencies": {
     |                           ^
  30 |   "@nurse-platform/backend-e2e": ["@nurse-platform/backend"]
  31 | },
  32 |     {

    at parseJson (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\json.js:29:15)
    at readJsonFile (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\utils\fileutils.js:32:37)
    at readNxJson (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\config\nx-json.js:12:66)
    at getPlugins (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\project-graph\plugins\get-plugins.js:23:59)
    at Timeout._onTimeout (D:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\daemon\server\project-graph-incremental-recomputation.js:93:98)
    at listOnTimeout (node:internal/timers:594:17)
    at process.processTimers (node:internal/timers:529:7)

Node.js v22.14.0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:51.322Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\f3183296a9513023b006\d.sock
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:51.326Z - [WATCHER]: Subscribed to changes within: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (native)
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:51.336Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:51.341Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:51.374Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:51.374Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:51.375Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:51.381Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:51.529Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:51.530Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:53.281Z - Time taken for 'Load Nx Plugin: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 1852.0851000000002ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:53.591Z - Time taken for 'Load Nx Plugin: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\package-json' 2166.5741ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:53.772Z - Time taken for 'loadDefaultNxPlugins' 2349.5836ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:54.935Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 3551.0049ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:55.873Z - Time taken for 'Load Nx Plugin: @nx/next/plugin' 4466.5587ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:55.952Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:55.953Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:55.953Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:55.970Z - Time taken for 'loadSpecifiedNxPlugins' 4567.9592ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:55.982Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:55.985Z - Established a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:55.986Z - Closed a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:55.995Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:55.995Z - Established a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:55.996Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:56.000Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:56.000Z - Handled HASH_GLOB. Handling time: 1. Response time: 5.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:56.000Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:56.003Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:56.008Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:56.009Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:56.009Z - Handled HASH_GLOB. Handling time: 0. Response time: 6.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:56.011Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:45:56.011Z - Handled HASH_GLOB. Handling time: 0. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.046Z - Time taken for 'build-project-configs' 12053.3542ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.049Z - Time taken for '@nx/js/typescript:createDependencies' 8.463700000000244ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.240Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.243Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.243Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.245Z - Time taken for 'total for creating and serializing project graph' 16862.2323ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.246Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.246Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 16863. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.251Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.252Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.254Z - Time taken for 'total for creating and serializing project graph' 1.0204999999987194ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.254Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.254Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.260Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.260Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.260Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 1. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.261Z - Closed a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:08.348Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:09.464Z - Established a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:09.465Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:09.465Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:09.467Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:09.468Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:09.469Z - Time taken for 'total for creating and serializing project graph' 0.879400000001624ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:09.588Z - [SYNC]: @nx/js:typescript-sync changes: tsconfig.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:09.588Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 1238.5620000000017ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:09.589Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:09.589Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 121.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.199Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.204Z - [WATCHER]: nx.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.321Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.321Z - [REQUEST]: nx.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.322Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.366Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.370Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.376Z - Time taken for 'hash changed files from watcher' 0.6134000000020023ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.377Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.378Z - Handled HASH_GLOB. Handling time: 8. Response time: 12.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.382Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.382Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.382Z - Handled HASH_GLOB. Handling time: 1. Response time: 12.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.387Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.387Z - Handled HASH_GLOB. Handling time: 1. Response time: 5.

 NX   Using `implicitDependencies` for global implicit dependencies configuration is no longer supported.

Use "namedInputs" instead. You can run "nx repair" to automatically migrate your configuration.
For more information about the usage of "namedInputs" see https://nx.dev/deprecated/global-implicit-dependencies#global-implicit-dependencies

[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.419Z - Time taken for 'build-project-configs' 73.45739999999932ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.422Z - Time taken for '@nx/js/typescript:createDependencies' 13.54200000000128ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.495Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.497Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.497Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.497Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.498Z - Time taken for 'total execution time for createProjectGraph()' 66.60779999999795ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.710Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.715Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.716Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.717Z - Time taken for 'total for creating and serializing project graph' 0.746899999998277ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.754Z - [SYNC]: @nx/js:typescript-sync changes: tsconfig.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.755Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 43.818299999999ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.755Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:33.755Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 39.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.227Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.228Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.228Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.229Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.229Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.232Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.233Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.235Z - Time taken for 'total for creating and serializing project graph' 1.275999999998021ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.235Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.235Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.246Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.247Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.247Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:34.247Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:39.855Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:39.855Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:39.856Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:39.861Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:39.862Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:39.863Z - Time taken for 'total for creating and serializing project graph' 1.1976999999969848ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:39.866Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:39.866Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:40.043Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:48.830Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:48.830Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:48.831Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:48.841Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:48.843Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:48.844Z - Time taken for 'total for creating and serializing project graph' 1.5072999999974854ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:48.846Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:48.846Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:48.856Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:48.857Z - Time taken for 'preTasksExecution' 0.6251000000047497ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:48.857Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:48.857Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:46:48.861Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.719Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.720Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.721Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.730Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.731Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.732Z - Time taken for 'total for creating and serializing project graph' 1.1825000000098953ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.735Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.735Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 4.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.744Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.745Z - Time taken for 'preTasksExecution' 0.5685999999986961ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.745Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.745Z - Handled PRE_TASKS_EXECUTION. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.888Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.889Z - Done responding to the client handleHashTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.889Z - Handled HASH_TASKS. Handling time: 33. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.978Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.979Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:21.979Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 18. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:30.947Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:30.948Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:30.949Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:30.952Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:30.953Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:30.954Z - Time taken for 'total for creating and serializing project graph' 1.2284999999974389ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:30.956Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:30.956Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:42.834Z - Established a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:42.835Z - Closed a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:42.836Z - Established a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:42.840Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:42.841Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:42.842Z - Time taken for 'total for creating and serializing project graph' 0.8367999999900348ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:42.844Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:42.844Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:43.461Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:43.462Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:43.464Z - Time taken for 'total for creating and serializing project graph' 1.2838000000047032ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:43.465Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:43.465Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.755Z - Closed a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.840Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.844Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.912Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.912Z - Done responding to the client handleRecordTaskRuns
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.912Z - Handled RECORD_TASK_RUNS. Handling time: 2. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.915Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.916Z - Done responding to the client handleGetFlakyTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.916Z - Handled GET_FLAKY_TASKS. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.923Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.924Z - Time taken for 'postTasksExecution' 0.9258000000118045ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.924Z - Done responding to the client handleRunPostTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.924Z - Handled POST_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:49.929Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:52.027Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:52.028Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:52.029Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:52.033Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:52.034Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:52.035Z - Time taken for 'total for creating and serializing project graph' 1.2465999999985797ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:52.038Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:52.039Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 5.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:53.226Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:47:53.274Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.362Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.407Z - [WATCHER]: apps/backend/src/main.ts was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.537Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.539Z - [REQUEST]: apps/backend/src/main.ts
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.539Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.556Z - Time taken for 'hash changed files from watcher' 1.9006999999983236ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.571Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.571Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.571Z - Handled HASH_GLOB. Handling time: 4. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.575Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.576Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.576Z - Handled HASH_GLOB. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.606Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.607Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.607Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.

 NX   Using `implicitDependencies` for global implicit dependencies configuration is no longer supported.

Use "namedInputs" instead. You can run "nx repair" to automatically migrate your configuration.
For more information about the usage of "namedInputs" see https://nx.dev/deprecated/global-implicit-dependencies#global-implicit-dependencies

[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.635Z - Time taken for 'build-project-configs' 70.76029999996535ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.637Z - Time taken for '@nx/js/typescript:createDependencies' 12.15380000002915ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.726Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.728Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.728Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.728Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:24.728Z - Time taken for 'total execution time for createProjectGraph()' 79.32490000000689ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.139Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.181Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.182Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.184Z - Time taken for 'total for creating and serializing project graph' 1.174799999978859ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.240Z - [SYNC]: @nx/js:typescript-sync changes: tsconfig.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.240Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 100.27950000000419ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.241Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.241Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 59.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.391Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.391Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.392Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.392Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.393Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.395Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.397Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.398Z - Time taken for 'total for creating and serializing project graph' 1.4408000000403263ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.399Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.399Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.407Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.408Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.408Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:50:25.409Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.248Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.249Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.249Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.312Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.313Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.313Z - Time taken for 'total for creating and serializing project graph' 1.0327999999863096ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.316Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.316Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.327Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.327Z - Time taken for 'preTasksExecution' 0.6314000000129454ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.328Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.328Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.337Z - [SYNC]: get sync generators changes on demand ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.338Z - [SYNC]: @nx/js:typescript-sync not scheduled and has cached result, returning cached result
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.338Z - [REQUEST]: Responding to the client. handleGetSyncGeneratorChanges
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.338Z - Done responding to the client handleGetSyncGeneratorChanges
[NX v21.2.1 Daemon Server] - 2025-06-24T11:51:28.339Z - Handled GET_SYNC_GENERATOR_CHANGES. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.720Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.724Z - [WATCHER]: nx.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.786Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.834Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.834Z - [REQUEST]: nx.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.834Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.842Z - Time taken for 'hash changed files from watcher' 0.44860000000335276ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.877Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.878Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.878Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.881Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.882Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.882Z - Handled HASH_GLOB. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.887Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.887Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.887Z - Handled HASH_GLOB. Handling time: 1. Response time: 0.

 NX   Using `implicitDependencies` for global implicit dependencies configuration is no longer supported.

Use "namedInputs" instead. You can run "nx repair" to automatically migrate your configuration.
For more information about the usage of "namedInputs" see https://nx.dev/deprecated/global-implicit-dependencies#global-implicit-dependencies

[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.950Z - Time taken for 'build-project-configs' 101.78109999996377ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:48.952Z - Time taken for '@nx/js/typescript:createDependencies' 8.133400000049733ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.005Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.006Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.007Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.007Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.007Z - Time taken for 'total execution time for createProjectGraph()' 47.491099999984726ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.115Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.171Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.172Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.173Z - Time taken for 'total for creating and serializing project graph' 0.7336999999824911ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.202Z - [SYNC]: @nx/js:typescript-sync changes: tsconfig.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.202Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 86.73139999998966ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.203Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.203Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 31.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.736Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.736Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.737Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.738Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.739Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.741Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.742Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.744Z - Time taken for 'total for creating and serializing project graph' 1.380200000014156ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.745Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.745Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.755Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.755Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.756Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 1. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:49.756Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:52:55.070Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.652Z - [WATCHER]: nx.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.653Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.764Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.765Z - [REQUEST]: nx.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.765Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.813Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.816Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.816Z - Time taken for 'hash changed files from watcher' 1.0165000000270084ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.827Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.827Z - Handled HASH_GLOB. Handling time: 33. Response time: 14.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.832Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.832Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.833Z - Handled HASH_GLOB. Handling time: 1. Response time: 17.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.834Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.834Z - Handled HASH_GLOB. Handling time: 3. Response time: 2.

 NX   Using `implicitDependencies` for global implicit dependencies configuration is no longer supported.

Use "namedInputs" instead. You can run "nx repair" to automatically migrate your configuration.
For more information about the usage of "namedInputs" see https://nx.dev/deprecated/global-implicit-dependencies#global-implicit-dependencies

[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.858Z - Time taken for 'build-project-configs' 71.79480000003241ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.860Z - Time taken for '@nx/js/typescript:createDependencies' 12.136199999949895ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.922Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.924Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.924Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.924Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:05.924Z - Time taken for 'total execution time for createProjectGraph()' 58.40950000000885ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.138Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.144Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.146Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.147Z - Time taken for 'total for creating and serializing project graph' 1.1836999999941327ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.190Z - [SYNC]: @nx/js:typescript-sync changes: tsconfig.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.190Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 51.41940000001341ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.191Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.191Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 45.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.673Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.673Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.674Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.674Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.674Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.676Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.678Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.679Z - Time taken for 'total for creating and serializing project graph' 1.4196999999694526ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.680Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.680Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.690Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.690Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.691Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:06.691Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.842Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.843Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.844Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.854Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.855Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.856Z - Time taken for 'total for creating and serializing project graph' 1.3306999999913387ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.858Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.858Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.870Z - [REQUEST]: Responding to the client. handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.870Z - Time taken for 'preTasksExecution' 0.5813000000198372ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.870Z - Done responding to the client handleRunPreTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.870Z - Handled PRE_TASKS_EXECUTION. Handling time: 1. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.877Z - [SYNC]: get sync generators changes on demand ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.877Z - [SYNC]: @nx/js:typescript-sync not scheduled and has cached result, returning cached result
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.877Z - [REQUEST]: Responding to the client. handleGetSyncGeneratorChanges
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.877Z - Done responding to the client handleGetSyncGeneratorChanges
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.877Z - Handled GET_SYNC_GENERATOR_CHANGES. Handling time: 1. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.923Z - [SYNC]: flush sync generators changes ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.923Z - [SYNC]: get sync generators changes on demand ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.924Z - [SYNC]: @nx/js:typescript-sync not scheduled and has cached result, returning cached result
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.926Z - [REQUEST]: Responding to the client. handleFlushSyncGeneratorChangesToDisk
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.926Z - Time taken for 'flush sync generator changes to disk' 1.8082000000285916ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.927Z - Done responding to the client handleFlushSyncGeneratorChangesToDisk
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.927Z - Handled CLEAR_CACHED_SYNC_GENERATOR_CHANGES. Handling time: 3. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.930Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.931Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.932Z - [REQUEST]: tsconfig.json
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.932Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.936Z - Time taken for 'hash changed files from watcher' 0.3850000000093132ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.953Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.957Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.959Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.959Z - Handled HASH_GLOB. Handling time: 5. Response time: 7.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.966Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.967Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.967Z - Handled HASH_GLOB. Handling time: 2. Response time: 10.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.971Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.971Z - Handled HASH_GLOB. Handling time: 0. Response time: 5.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.982Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:13.990Z - [WATCHER]: tsconfig.json was modified

 NX   Using `implicitDependencies` for global implicit dependencies configuration is no longer supported.

Use "namedInputs" instead. You can run "nx repair" to automatically migrate your configuration.
For more information about the usage of "namedInputs" see https://nx.dev/deprecated/global-implicit-dependencies#global-implicit-dependencies

[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.005Z - Time taken for 'build-project-configs' 59.35690000001341ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.006Z - Time taken for '@nx/js/typescript:createDependencies' 8.691099999996368ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.050Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.053Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.054Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.054Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.054Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.056Z - Time taken for 'total for creating and serializing project graph' 124.43900000001304ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.057Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.057Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 124. Response time: 3.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.101Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.101Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.101Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.106Z - Time taken for 'hash changed files from watcher' 0.1426000000210479ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.116Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.142Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.142Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.142Z - Handled HASH_GLOB. Handling time: 3. Response time: 26.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.146Z - [REQUEST]: Responding to the client. handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.153Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.153Z - Handled HASH_GLOB. Handling time: 23. Response time: 11.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.154Z - Done responding to the client handleHashGlob
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.154Z - Handled HASH_GLOB. Handling time: 1. Response time: 8.

 NX   Using `implicitDependencies` for global implicit dependencies configuration is no longer supported.

Use "namedInputs" instead. You can run "nx repair" to automatically migrate your configuration.
For more information about the usage of "namedInputs" see https://nx.dev/deprecated/global-implicit-dependencies#global-implicit-dependencies

[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.171Z - Time taken for 'build-project-configs' 53.91069999994943ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.172Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.177Z - Time taken for '@nx/js/typescript:createDependencies' 14.746800000022631ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.180Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.211Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.212Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.212Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.212Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.213Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.213Z - Time taken for 'total execution time for createProjectGraph()' 36.34179999999469ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.251Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.251Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 78.79180000000633ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.255Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.255Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 33. Response time: 42.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.414Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.418Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.419Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.420Z - Time taken for 'total for creating and serializing project graph' 0.7274000000325032ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.456Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.456Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 41.2776999999769ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.456Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.456Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 37.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.613Z - [REQUEST]: Responding to the client. handleHashTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.614Z - Done responding to the client handleHashTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.614Z - Handled HASH_TASKS. Handling time: 27. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.674Z - [REQUEST]: Responding to the client. handleGetEstimatedTaskTimings
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.674Z - Done responding to the client handleGetEstimatedTaskTimings
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:14.674Z - Handled GET_ESTIMATED_TASK_TIMINGS. Handling time: 0. Response time: 0.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.013Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.014Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.014Z - Established a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.014Z - Established a connection. Number of open connections: 6
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.015Z - Closed a connection. Number of open connections: 5
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.017Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.018Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.019Z - Time taken for 'total for creating and serializing project graph' 1.1644000000087544ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.020Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.020Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.029Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.030Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.030Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:15.031Z - Closed a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:35.146Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:37.977Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:37.987Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:37.987Z - Done responding to the client recordOutputsHash
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:37.988Z - Handled RECORD_OUTPUTS_HASH. Handling time: 5. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:38.474Z - [REQUEST]: Responding to the client. handleRecordTaskRuns
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:38.475Z - Done responding to the client handleRecordTaskRuns
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:38.475Z - Handled RECORD_TASK_RUNS. Handling time: 478. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:38.477Z - [REQUEST]: Responding to the client. handleGetFlakyTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:38.478Z - Done responding to the client handleGetFlakyTasks
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:38.478Z - Handled GET_FLAKY_TASKS. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:38.486Z - [REQUEST]: Responding to the client. handleRunPostTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:38.486Z - Time taken for 'postTasksExecution' 0.6331999999820255ms
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:38.490Z - Done responding to the client handleRunPostTasksExecution
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:38.490Z - Handled POST_TASKS_EXECUTION. Handling time: 0. Response time: 4.
[NX v21.2.1 Daemon Server] - 2025-06-24T11:53:38.494Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.477Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.478Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.480Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.481Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.481Z - Established a connection. Number of open connections: 4
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.571Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.572Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.573Z - Time taken for 'total for creating and serializing project graph' 1.280799999833107ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.574Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.574Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 2. Response time: 2.
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.582Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.583Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.583Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T13:43:09.584Z - Closed a connection. Number of open connections: 3
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:25.690Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:26.091Z - Closed a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:26.091Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:26.091Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:26.102Z - [WATCHER]: Stopping the watcher for d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (sources)
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:26.103Z - [WATCHER]: Stopping the watcher for d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (outputs)
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:28.548Z - Server stopped because: "LOCK_FILES_CHANGED"
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:42.908Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\f3183296a9513023b006\d.sock
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:42.914Z - [WATCHER]: Subscribed to changes within: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (native)
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:42.921Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:42.925Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:42.963Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:42.964Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:42.966Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:42.971Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:46.686Z - Time taken for 'Load Nx Plugin: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\package-json' 3699.9284000000002ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:47.312Z - Time taken for 'Load Nx Plugin: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 4324.5704000000005ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:47.502Z - Time taken for 'loadDefaultNxPlugins' 4517.689899999999ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:48.855Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 5881.5198ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:49.199Z - [WATCHER]: package.json was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:49.216Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:50.070Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:50.083Z - [WATCHER]: apps/frontend/.gitignore was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:50.084Z - [WATCHER]: Stopping the watcher for d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (sources)
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:50.085Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:50.086Z - [WATCHER]: Stopping the watcher for d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (outputs)
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:50.101Z - Server stopped because: "Stopping the daemon the set of ignored files changed (native)"
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:56.638Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\f3183296a9513023b006\d.sock
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:56.644Z - [WATCHER]: Subscribed to changes within: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (native)
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:56.652Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:56.657Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:56.692Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:56.692Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:56.693Z - Established a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:56.698Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:56.830Z - Established a connection. Number of open connections: 2
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:56.831Z - Closed a connection. Number of open connections: 1
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:59.016Z - Time taken for 'Load Nx Plugin: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\project-json\build-nodes\project-json' 2296.3878999999997ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:59.267Z - Time taken for 'Load Nx Plugin: d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform\node_modules\nx\src\plugins\package-json' 2550.8249ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:56:59.359Z - Time taken for 'loadDefaultNxPlugins' 2645.8667ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:00.141Z - Time taken for 'Load Nx Plugin: @nx/js/typescript' 3439.6544ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:00.425Z - Time taken for 'Load Nx Plugin: @nx/next/plugin' 3719.2355ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:00.660Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:00.660Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:00.660Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:00.690Z - Time taken for 'loadSpecifiedNxPlugins' 3957.2585ms

 NX   Using `implicitDependencies` for global implicit dependencies configuration is no longer supported.

Use "namedInputs" instead. You can run "nx repair" to automatically migrate your configuration.
For more information about the usage of "namedInputs" see https://nx.dev/deprecated/global-implicit-dependencies#global-implicit-dependencies

[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.093Z - Time taken for 'build-project-configs' 24330.811200000004ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.094Z - Time taken for '@nx/js/typescript:createDependencies' 16.005199999999604ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.418Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.422Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.423Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.436Z - Time taken for 'total for creating and serializing project graph' 28724.5315ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.438Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.438Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 28725. Response time: 15.
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.443Z - [REQUEST]: Client Request for Project Graph Received
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.444Z - [REQUEST]: Responding to the client. project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.452Z - Time taken for 'total for creating and serializing project graph' 1.349000000001979ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.453Z - Done responding to the client project-graph
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.453Z - Handled REQUEST_PROJECT_GRAPH. Handling time: 1. Response time: 9.
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.467Z - [REQUEST]: Responding to the client. handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.468Z - Done responding to the client handleContextFileData
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.468Z - Handled GET_CONTEXT_FILE_DATA. Handling time: 0. Response time: 1.
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.470Z - Closed a connection. Number of open connections: 0
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:25.532Z - [SYNC]: running scheduled generator @nx/js:typescript-sync

 NX   Using `implicitDependencies` for global implicit dependencies configuration is no longer supported.

Use "namedInputs" instead. You can run "nx repair" to automatically migrate your configuration.
For more information about the usage of "namedInputs" see https://nx.dev/deprecated/global-implicit-dependencies#global-implicit-dependencies

[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:27.021Z - Time taken for 'build-project-configs' 43.54979999999705ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:27.028Z - Time taken for '@nx/js/typescript:createDependencies' 16.614199999999983ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:27.323Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:27.323Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 1789.9558999999972ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.204Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.212Z - [WATCHER]: apps/backend/src/main.ts was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.319Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.319Z - [REQUEST]: apps/backend/src/main.ts
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.319Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.339Z - Time taken for 'hash changed files from watcher' 0.9916000000011991ms

 NX   Using `implicitDependencies` for global implicit dependencies configuration is no longer supported.

Use "namedInputs" instead. You can run "nx repair" to automatically migrate your configuration.
For more information about the usage of "namedInputs" see https://nx.dev/deprecated/global-implicit-dependencies#global-implicit-dependencies

[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.547Z - Time taken for 'build-project-configs' 197.25910000000295ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.556Z - Time taken for '@nx/js/typescript:createDependencies' 28.783699999999953ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.720Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.723Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.724Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.724Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.725Z - Time taken for 'total execution time for createProjectGraph()' 124.75390000000334ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:39.955Z - [SYNC]: running scheduled generator @nx/js:typescript-sync

 NX   Using `implicitDependencies` for global implicit dependencies configuration is no longer supported.

Use "namedInputs" instead. You can run "nx repair" to automatically migrate your configuration.
For more information about the usage of "namedInputs" see https://nx.dev/deprecated/global-implicit-dependencies#global-implicit-dependencies

[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:40.287Z - Time taken for 'build-project-configs' 230.6521999999968ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:40.289Z - Time taken for '@nx/js/typescript:createDependencies' 12.395700000000943ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:40.540Z - [SYNC]: @nx/js:typescript-sync changes: 
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:40.541Z - Time taken for 'run-sync-generator:@nx/js:typescript-sync' 585.6144000000058ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:43.159Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:43.257Z - [WATCHER]: apps/backend/src/main.ts was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:43.501Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:43.501Z - [REQUEST]: apps/backend/src/main.ts
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:43.501Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:43.770Z - Time taken for 'hash changed files from watcher' 0.5253000000011525ms

 NX   Using `implicitDependencies` for global implicit dependencies configuration is no longer supported.

Use "namedInputs" instead. You can run "nx repair" to automatically migrate your configuration.
For more information about the usage of "namedInputs" see https://nx.dev/deprecated/global-implicit-dependencies#global-implicit-dependencies

[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:43.896Z - Time taken for 'build-project-configs' 129.65520000000106ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:43.899Z - Time taken for '@nx/js/typescript:createDependencies' 22.089499999994587ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:44.268Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:44.270Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:44.270Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:44.270Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:44.270Z - Time taken for 'total execution time for createProjectGraph()' 98.26800000000367ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:44.702Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:50.830Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:50.925Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:52.376Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:52.414Z - [WATCHER]: apps/frontend/next.config.js was modified
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:52.817Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:52.817Z - [REQUEST]: apps/frontend/next.config.js
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:52.817Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:52.897Z - Time taken for 'hash changed files from watcher' 0.43299999999726424ms

 NX   Using `implicitDependencies` for global implicit dependencies configuration is no longer supported.

Use "namedInputs" instead. You can run "nx repair" to automatically migrate your configuration.
For more information about the usage of "namedInputs" see https://nx.dev/deprecated/global-implicit-dependencies#global-implicit-dependencies

[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:52.976Z - Time taken for 'build-project-configs' 92.16489999999612ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:52.978Z - Time taken for '@nx/js/typescript:createDependencies' 12.39259999999922ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:53.145Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:53.147Z - [SYNC]: project graph hash is the same, not collecting task sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:53.147Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:53.147Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:53.148Z - Time taken for 'total execution time for createProjectGraph()' 107.02509999999893ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:57:53.960Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:00.116Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:12.364Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:12.367Z - [WATCHER]: apps/nurse-platform was deleted
[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:13.207Z - [REQUEST]: Updated workspace context based on watched changes, recomputing project graph...
[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:13.209Z - [REQUEST]: 
[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:13.209Z - [REQUEST]: apps/nurse-platform
[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:13.359Z - Time taken for 'hash changed files from watcher' 10.041599999996834ms

 NX   Using `implicitDependencies` for global implicit dependencies configuration is no longer supported.

Use "namedInputs" instead. You can run "nx repair" to automatically migrate your configuration.
For more information about the usage of "namedInputs" see https://nx.dev/deprecated/global-implicit-dependencies#global-implicit-dependencies

[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:13.408Z - Time taken for 'build-project-configs' 54.2387000000017ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:13.409Z - Time taken for '@nx/js/typescript:createDependencies' 17.68409999999858ms
[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:13.841Z - [SYNC]: collect registered sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:13.844Z - [SYNC]: nx.json hash is the same, not collecting global sync generators
[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:13.844Z - [SYNC]: scheduling: ["@nx/js:typescript-sync"]
[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:13.844Z - Time taken for 'total execution time for createProjectGraph()' 395.46199999999953ms
Waiting for graph construction in another process to complete
[NX v21.2.1 Daemon Server] - 2025-06-24T13:58:15.487Z - [SYNC]: running scheduled generator @nx/js:typescript-sync
Waiting for graph construction in another process to complete
Waiting for graph construction in another process to complete
[NX v21.2.1 Daemon Server] - 2025-06-24T14:00:06.630Z - [WATCHER]: Processing file changes in outputs
[NX v21.2.1 Daemon Server] - 2025-06-24T14:00:06.660Z - [WATCHER]: Stopping the watcher for d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (sources)
[NX v21.2.1 Daemon Server] - 2025-06-24T14:00:06.661Z - [WATCHER]: Stopping the watcher for d:\Intake45_ITI_9-months_Open_Source\Graduation-Project-ITI\nurse-platform (outputs)
[NX v21.2.1 Daemon Server] - 2025-06-24T14:00:06.668Z - Server stopped because: "LOCK_FILES_CHANGED"
